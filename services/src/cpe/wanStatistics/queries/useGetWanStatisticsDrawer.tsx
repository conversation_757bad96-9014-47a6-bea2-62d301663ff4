import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../../types';
import { getWanStatisticsDrawer } from '../apis/getWanStatisticsDrawer';
import { QUERY_KEYS } from '../constants/queryKeys';
import { IWanStatisticDrawerResponseData, IWanStatisticsQuery } from '../types';

export const useGetWanStatisticsDrawer = (
  { deviceId, customerId, startDate, endDate }: IWanStatisticsQuery,
  options?: AxonQueryOptions<BFFResponseDTO<IWanStatisticDrawerResponseData>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.WAN_STATISTICS_DRAWER({ deviceId, customerId, startDate, endDate }),
    queryFn: () => getWanStatisticsDrawer({ deviceId, customerId, startDate, endDate }),
    ...options,
  });
};
