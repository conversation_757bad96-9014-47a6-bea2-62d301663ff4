export interface CpeHistoryResponse {
  datapoints: TCpeHistoryDataPoint[];
}

export type TCpeHistoryDataPoint = {
  date: number;
  cpuUsage?: number | null;
  cpuLoad?: number | null;
  freeMemory?: number | null;
  chipsetTemperature?: number | null;
  powerCycle?: number | null;
  qoe?: number | null;
  rssi?: {
    [deviceId: string]: {
      [bandId: string]: number | null;
    } | null;
  };
  disconnEvents?: {
    [deviceId: string]: {
      [bandId: string]: number | null;
    } | null;
  };
  reconnectEvents?: {
    [deviceId: string]: {
      [bandId: string]: number | null;
    } | null;
  };
};

export interface CpeHistoryRequestParams {
  startDate: number;
  endDate?: number;
  deviceId: string;
  customerId: string;
}
