import { DeepPartial } from '../types/index';
export type GetLineInfo = DeepPartial<{
  isp: ISP;
  network: NetworkLineDTO;
}>;

export type NetworkLineDTO = {
  lineId: string | null;
  name: string | null;
  customerId: string | null;
  subscribedSince: string | null;
  networkOwned: string | null;
  membershipLevel: string | null;
  overduePayments: OverduePayments;
  passCallHistory: PassCallHistory;
  lastCalledOn: string | null;
  lastCalledDayAgo: number | null;
  badges: {
    isVip: boolean | null;
    isHighARPU: boolean | null;
    heavyUserPercentage: number | null;
  };
  phoneNumber: number;
  servicePlanName: string;
  config: {
    enableRealtimeCollectData: boolean;
  };
};

export type OverduePayments = {
  days: number;
  daysToSuspend: number;
};

export type PassCallHistory = {
  last30Days: number;
  monthBefore: number;
};

export type ISP = {
  name: string | null;
  city: string | null;
  timezone: string | null;
  timezoneUTC: string | null;
  wanType: string | null;
  connectedExtenders: {
    supported: number | null;
    unsupported: number | null;
  };
  connectedClients: {
    ethernet: number | null;
    wifi: number | null;
  };
};
