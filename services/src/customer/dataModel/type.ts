type TypeWithNullValue<T> = T | null;

interface Client {
  parentDeviceName: TypeWithNullValue<string>;
  device: {
    name: string;
    deviceType: TypeWithNullValue<string>;
    label: TypeWithNullValue<'axon'>;
    statuses: {
      qoe: {
        status: TypeWithNullValue<number>;
        lastTested: TypeWithNullValue<number>;
      };
      online: {
        isAlive: TypeWithNullValue<boolean>;
        lastTested: TypeWithNullValue<number>;
      };
    };
  };
  deviceInfo: {
    modelName: TypeWithNullValue<string>;
    osVersion: TypeWithNullValue<string>;
  };
  connection: {
    connectedInterface: TypeWithNullValue<string>;
    connectedBandName: TypeWithNullValue<string>;
    connectedMode: TypeWithNullValue<string>;
    connectionType: TypeWithNullValue<string>;
    coverageDetectionStatus: TypeWithNullValue<string>;
    rssi: TypeWithNullValue<number>;
  };
  ipMac: {
    ipAddress: TypeWithNullValue<string>;
    stationMac: TypeWithNullValue<string>;
  };
  speed: {
    upload: TypeWithNullValue<number>;
    download: TypeWithNullValue<number>;
    unit: string;
    latency: TypeWithNullValue<string>;
    lastTested: TypeWithNullValue<number>;
  };
  counts: {
    insights: TypeWithNullValue<number>;
    logs: TypeWithNullValue<number>;
  };
}

export interface DataModelResponse {
  result: Client[];
}
