import { useQuery } from '@tanstack/react-query';
import { getTopologyHistory } from '../api/getTopologyHistory';
import { queryKeys } from '../queryKeys';
import { IGetTopologyHistoryParams, ITopologyHistory } from '../types/NetworkTopology';
import { AxonQueryOptions, BFFResponseDTO } from './../../types/index';

export const useGetTopologyHistory = (
  { customerId, startDate, endDate }: IGetTopologyHistoryParams,
  option?: AxonQueryOptions<BFFResponseDTO<ITopologyHistory>>,
) => {
  return useQuery({
    queryKey: queryKeys.useGetTopologyHistory({ customerId, startDate, endDate }),
    queryFn: () => getTopologyHistory({ customerId, startDate, endDate }),
    ...option,
  });
};
