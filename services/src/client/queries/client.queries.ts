import { useQuery } from '@tanstack/react-query';
import { getClientConnection, getClientHistory } from '../apis/client.api';
import { QUERY_KEYS } from '../constants/queryKeys';
import { ClientConnectionResponse, ClientHistoryParams, ClientHistoryResponse } from '../types/client.type';
import { AxonQueryOptions, BFFResponseDTO } from './../../types';

export const useGetClientHistory = (
  params: ClientHistoryParams,
  options?: AxonQueryOptions<BFFResponseDTO<ClientHistoryResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CLIENT_HISTORY(params),
    queryFn: () => getClientHistory(params),
    ...options,
  });
};

export const useGetClientConnection = (
  params: ClientHistoryParams,
  options?: AxonQueryOptions<BFFResponseDTO<ClientConnectionResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CLIENT_CONNECTION(params),
    queryFn: () => getClientConnection(params),
    ...options,
  });
};
