import { EHealthCheckStatus, ConnectionType } from '@/healthCheck/types/healthCheck.types';

export interface CpeClient {
  name: string;
  mac: string;
  type: string;
  throughputBandAvailable: Record<string, boolean>;
  latencyBandAvailable: Record<string, boolean>;
  rssiBandAvailable: Record<string, boolean>;
  snrBandAvailable: Record<string, boolean>;
  txPhyRateBandAvailable: Record<string, boolean>;
  rxPhyRateBandAvailable: Record<string, boolean>;
  trafficUpBandAvailable: Record<string, boolean>;
  trafficDownBandAvailable: Record<string, boolean>;
  qoeBandAvailable: boolean;
}

export interface CpeClientStation {
  rssi: number | null;
  band: string;
  snr: number | null;
  throughput: number | null;
  latency: number | null;
}

export interface CpeClientHistoryDatapoint {
  date: number;
  stations: Record<string, Record<string, CpeClientStation> | { qoe: number }>;
}

export interface CpeBand {
  bandId: string;
  band: string;
  label: string;
  networkType: string;
  connectionType: ConnectionType;
}

export interface ClientHistoryResponse {
  datapoints: CpeClientHistoryDatapoint[];
  clients: CpeClient[];
  bands: CpeBand[];
}

export type ClientHistoryParams = {
  customerId: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
};

interface DeviceInfo {
  deviceName: string;
  isOnline: boolean;
  lastSeenTimestamp: number; // UNIX timestamp
  deviceType: string; // e.g. 'iphone', 'tv', 'laptop'
}

interface NetworkConnection {
  connectionType: ConnectionType;
  band: string;
  description: string; // e.g., SSID or Ethernet port name
  connectionInterface: string | null;
}

interface NetworkAddress {
  ipAddress: string;
  macAddress: string;
}

interface DataUsageInfo {
  totalUsedGB: number; // e.g., 1.5
  percentChangeFromLastWeek: number; // e.g., 31
}

interface InsightStats {
  eventsCount: number;
  lastUpdatedTimestamp: number; // UNIX timestamp
}

interface SignalStrength {
  rssiValue: number; // e.g., -40 (strong) to -90 (weak)
  coverageDetection: number;
  status: EHealthCheckStatus;
}

export interface DeviceRecord {
  cpeId: string;
  deviceInfo: DeviceInfo;
  networkConnection: NetworkConnection;
  networkAddress: NetworkAddress;
  dataUsage: DataUsageInfo;
  insights: InsightStats;
  signalStrength: SignalStrength;
  parentalControls: {
    enabled: boolean;
    stationMac: string;
    indefinite: boolean;
    parentalRestrictionId: string;
  }[];
}

export interface ClientConnectionResponse {
  results: DeviceRecord[];
  cpeIds: string[];
  deviceTypes: string[];
  connectionInterfaces: string[];
}
