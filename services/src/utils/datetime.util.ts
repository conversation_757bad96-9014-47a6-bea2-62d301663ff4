import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/en';
import 'dayjs/locale/de';
import i18n from '@/i18n';

dayjs.extend(updateLocale);
dayjs.extend(utc);
dayjs.extend(isSameOrBefore);
dayjs.extend(timezone);

const currentLang = localStorage.getItem('lang') ?? process.env.LANGUAGE_ENV ?? 'de';
dayjs.locale(currentLang);

const thresholds = [
  { l: 's', r: 1 },
  { l: 'm', r: 1 },
  { l: 'mm', r: 59, d: 'minute' },
  { l: 'h', r: 1 },
  { l: 'hh', r: 23, d: 'hour' },
  { l: 'd', r: 1 },
  { l: 'dd', r: 99999, d: 'day' },
];

dayjs.extend(relativeTime, {
  thresholds,
});

dayjs.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    m: '1 minute',
    mm: '%d minutes',
    h: '1 hour',
    hh: '%d hours',
    d: '1 day',
    dd: '%d days',
    M: '1 month',
    MM: '%d months',
    y: '1 year',
    yy: '%d years',
  },
});

let currentTz: string = dayjs.tz.guess();

const isValidTimezone = (tz: string): boolean => {
  try {
    Intl.DateTimeFormat('en-US', { timeZone: tz }).format();
    return true;
  } catch {
    return false;
  }
};

export const setTimezone = (tz: string): void => {
  if (!isValidTimezone(tz)) {
    return;
  }
  currentTz = tz;
};

export const getTimezone = (): string => currentTz;

export { dayjs };

export function dayjsTz(input?: dayjs.ConfigType, format?: dayjs.OptionType) {
  return dayjs(input, format).tz(currentTz);
}

type TDate = Date | string | number | null | undefined;

/**
 * Get the unix time from a date in milliseconds
 * @param date - The date to get the unix time from
 * @returns The unix time
 */
export const getUnixTime = (date: TDate) => {
  try {
    // check if date is a valid date
    if (dayjs(date).isValid()) {
      return dayjs(date).valueOf();
    }
    return null;
  } catch (error) {
    return null;
  }
};

export const getDayjsFromUnixTime = (unixTime: number) => {
  try {
    if (!unixTime) {
      return null;
    }
    const isUnixTimeMilliSeconds = unixTime.toString().length > 10;
    return isUnixTimeMilliSeconds ? dayjs(unixTime) : dayjs.unix(unixTime);
  } catch (error) {
    return null;
  }
};

const MAX_MINUTES_FOR_MINUTE_INTERVAL = 120;

const MAX_MINUTES_FOR_HOURS_INTERVAL = 2880;

const ONE_DAY_IN_MINUTES = 1440;

const DAY_ONLY_IN_MINUTES = 1440;

/**
 * Get the relative time from now
 * @param unixTime - The time to get the relative time from
 * @param dayOnly - Whether to show the day only
 * @returns The relative time from now
 */
export const getRelativeTimeFromNow = (unixTime: number, dayOnly = false) => {
  try {
    const today = getDayjsFromToday(0)!;
    const dateInDayjs = getDayjsFromUnixTime(unixTime);
    if (!dateInDayjs) return null;

    const minutes = today.diff(dateInDayjs, 'minute');
    let translationKey = '';
    let value: number | null = null;

    if (dayOnly) {
      if (isToday(unixTime)) {
        return i18n.t('time_unit:today');
      }
      if (minutes >= DAY_ONLY_IN_MINUTES) {
        const days = Math.floor(minutes / ONE_DAY_IN_MINUTES);
        translationKey = days === 1 ? 'time_unit:one_day' : 'time_unit:days';
        value = days;
      } else {
        // not today and less than 1 day -> assume it is 1 day
        translationKey = 'time_unit:one_day';
        value = 1;
      }
      return i18n.t(translationKey, { value });
    }

    // fallback for non-dayOnly mode
    if (minutes === 0) {
      translationKey = 'time_unit:less_than_one_minute';
    } else if (minutes === 1) {
      translationKey = 'time_unit:one_minute';
    } else if (minutes < MAX_MINUTES_FOR_MINUTE_INTERVAL) {
      translationKey = 'time_unit:minutes';
      value = minutes;
    } else if (minutes < MAX_MINUTES_FOR_HOURS_INTERVAL) {
      const hours = Math.floor(minutes / 60);
      translationKey = 'time_unit:hours';
      value = hours;
    } else {
      const days = Math.floor(minutes / ONE_DAY_IN_MINUTES);
      translationKey = 'time_unit:days';
      value = days;
    }

    return i18n.t(translationKey, { value });
  } catch {
    return null;
  }
};

/**
 * Get the relative time to now
 * @param unixTime - The unix time to get the relative time to
 * @param withoutSuffix - Whether to show the suffix
 * @returns The relative time to now
 */
export const getRelativeTimeToNow = (unixTime: number, withoutSuffix?: boolean) => {
  try {
    const time = getDayjsFromUnixTime(unixTime);
    if (time === null) {
      return null;
    }
    // Set the locale for this instance only
    return time.fromNow(withoutSuffix);
  } catch (error) {
    return null;
  }
};

/**
 * Subtract a number of days from a date
 * @param days - The date to subtract from in unix time with seconds.
 * @param amount - The number of days to subtract
 * @param unit - The unit of time to subtract from
 * @returns The date after subtracting the number of days
 */
export const subtract = (days: number, amount: number, unit?: ManipulateType) => {
  try {
    const date = getDayjsFromUnixTime(days);
    if (date === null) {
      return null;
    }
    return date.subtract(amount, unit);
  } catch (error) {
    return null;
  }
};

/**
 * Check if the unix time is today
 * @param unixTime - The unix time to check in seconds
 * @returns Whether the unix time is today
 */
export const isToday = (unixTime: number) => {
  try {
    const date = getDayjsFromUnixTime(unixTime);
    if (date === null) {
      return false;
    }
    return date.isSame(dayjs(), 'day');
  } catch (error) {
    return false;
  }
};

/**
 * Format the date
 * @param unixTime - The unix time to format in seconds
 * @param format - The format to format the date
 * @returns The formatted date
 */
export const formatDate = (tDate?: TDate, format?: string, isUtc = false) => {
  try {
    const date = dayjs(tDate);
    if (date === null) {
      return null;
    }
    return isUtc ? date.utc().format(format) : date.tz(currentTz).format(format);
  } catch (error) {
    return null;
  }
};

/**
 * Get the date from today
 * @param timeRange - The time range to get the date from
 * @param unit - The unit of time to get the date from
 * @param atMidnight - Whether to get the date at midnight.
 * @returns The date from today
 */
export const getDayjsFromToday = (
  timeRange: number,
  defaultUnit?: ManipulateType,
  atMidnight = false,
): Dayjs | null => {
  try {
    const unit = defaultUnit || 'day';
    let date = dayjs().subtract(timeRange, unit);
    if (atMidnight) {
      // start of the day.
      date = date.startOf('day');
    }
    return date;
  } catch (error) {
    return null;
  }
};

/**
 * Get the day from today by weeks
 * @param week - The week to get the day from. Date format should be like '1W', '2W', '3W', '4W'
 * @returns The day from today by weeks
 */
export const getDayjsFromTodayByWeeks = (week: string): Dayjs | null => {
  try {
    const weeks = Number(week.replace('W', ''));
    return getDayjsFromToday(weeks, 'week');
  } catch (error) {
    return null;
  }
};

/**
 * Get the day from today by days
 * @param day - The day to get the day from. Date format should be like '1D', '2D', '3D', '4D'
 * @returns The day from today by days
 */
export const getDayjsFromTodayByDays = (day: string): Dayjs | null => {
  try {
    const days = Number(day.replace('D', ''));
    return getDayjsFromToday(days, 'day');
  } catch (error) {
    return null;
  }
};

export const getDayjsFromDate = (date: TDate): Dayjs => {
  return dayjs(date);
};

enum DATETIME_FORMAT_DE {
  DATE = 'DD. MMMM YYYY',
  DATE_TIME = 'DD. MMMM YYYY HH:mm',
  DATE_TIME_CSV = 'YYYY-MMM-DD HH:mm',
  TIME_HOUR = 'HH:mm',
  MONTH_DAY = 'DD. MMMM',
  DAY_OF_WEEK = 'dddd, DD. MMMM',
}

enum DATETIME_FORMAT_EN {
  DATE = 'MMM DD, YYYY',
  DATE_TIME = 'MMM DD, YYYY HH:mm',
  DATE_TIME_CSV = 'YYYY-MMM-DD HH:mm',
  TIME_HOUR = 'HH:mm',
  MONTH_DAY = 'MMM DD',
  DAY_OF_WEEK = 'dddd, MMMM DD',
}

export const DATETIME_FORMAT = currentLang === 'en' ? DATETIME_FORMAT_EN : DATETIME_FORMAT_DE;

export const isSameDay = (date1: TDate, date2: TDate) => {
  try {
    const day1 = dayjs(date1);
    const day2 = dayjs(date2);
    return day1.isSame(day2, 'day');
  } catch (error) {
    return false;
  }
};
