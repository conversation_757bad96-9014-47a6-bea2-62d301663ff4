import { useLogout } from '../user';
import { useIdleTimer } from 'react-idle-timer';
import { useNavigate } from 'react-router-dom';

const TIMEOUT = process.env.USER_TIMEOUT || '15'; // in minutes

export const useIdleLogout = () => {
  const navigate = useNavigate();
  const userTimeout =
    process.env.NODE_ENV === 'production' && localStorage.getItem('remember') !== 'true'
      ? parseInt(TIMEOUT) * 60 * 1000
      : undefined;
  const { mutate: logout } = useLogout({
    onSettled: () => {
      localStorage.removeItem('accessToken');
      navigate('/');
    },
  });

  useIdleTimer({
    timeout: userTimeout,
    onIdle: () => logout(),
    debounce: 1000,
  });
};
