import { WifiStatisticsParameterQueryParameters } from '../../wifiStatistics/types/WifiStatistics';

export type RealtimeParams = {
  customerId: string;
  realtimeRequestId?: string;
  activeProbing?: boolean;
  isActiveWifiSpeedtest?: boolean;
};

export interface IRealtimeRequestIdRes {
  requestId: string;
}

export interface IRealtimeStatus {
  status?: 'SUCCESS' | 'FAILED' | 'IN_PROGRESS';
  realtimeRequestFinished: boolean;
  realtimeRequestId: string;
  lastUpdated: number | null;
  currentStepId?: string;
  currentStepText?: string;
}

export type RealtimeMetric = 'healthCheck' | 'insight' | 'all';

export interface RealtimeInsightParams extends Required<RealtimeParams> {
  startDate: number;
  deviceId?: string;
}

export interface RealtimeSpeedtestParams extends WifiStatisticsParameterQueryParameters {
  realtimeRequestId: string;
}
