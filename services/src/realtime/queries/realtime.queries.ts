import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { useQuery } from '@tanstack/react-query';
import { RealtimeParams, RealtimeInsightParams, RealtimeSpeedtestParams } from '../types/realtime.type';
import { QUERY_KEYS } from '../constants/realtime.queryKeys';
import {
  getRealtimeDataRequestId,
  getRealtimeDataStatus,
  getHealthcheckRealtime,
  getInsightRealtime,
  getWifiSpeedtestRealtime,
} from '../api/realtime.api';
import { IHealthCheck } from '@/healthCheck/types/healthCheck.types';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';
import { useCallback } from 'react';
import { WifiStatisticsParameterResponse } from '@/wifiStatistics/types/WifiStatistics';

export const useStartRealtimeProcess = () => {
  const startRealtimeProcess = useCallback(
    async (params: RealtimeParams, callback: (path: string, config: any, customerId: string) => void) => {
      try {
        const requestIdRes = await getRealtimeDataRequestId(params);
        const requestId = get(requestIdRes, 'data.requestId') ?? '';
        const statusRes = await getRealtimeDataStatus(
          {
            ...params,
            realtimeRequestId: requestId,
            activeProbing: Boolean(params.activeProbing),
            isActiveWifiSpeedtest: Boolean(params.isActiveWifiSpeedtest),
          },
          (res) => {
            callback('realtime.currentStepId', res.currentStepId, params.customerId);
            callback('realtime.currentStepText', res.currentStepText, params.customerId);
          },
        );
        const status = get(statusRes, 'data.status') ?? '';
        const dataIsReady = status === 'SUCCESS';
        const lastUpdated = get(statusRes, 'data.lastUpdated') ?? null;
        callback('realtime.enableRealtimeRequesting', false, params.customerId);
        callback('realtime.realtimeRequestId', requestId, params.customerId);
        callback('realtime.dataIsReady', dataIsReady, params.customerId);
        callback('realtime.lastUpdated', lastUpdated, params.customerId);
      } catch (error) {
        callback('realtime.enableRealtimeRequesting', false, params.customerId);
        callback('realtime.realtimeRequestId', '', params.customerId);
        callback('realtime.dataIsReady', false, params.customerId);
      }
    },
    [],
  );
  return {
    startRealtimeProcess,
  };
};

export const useGetRealtimeHealthcheck = (
  params: Required<RealtimeParams>,
  options?: AxonQueryOptions<BFFResponseDTO<IHealthCheck>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeHealthcheck(params),
    queryFn: () => getHealthcheckRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    retry: false,
    staleTime: Infinity,
    gcTime: Infinity,
    ...options,
  });
};

export const useGetRealtimeInsight = (
  params: RealtimeInsightParams,
  options?: AxonQueryOptions<BFFResponseDTO<InsightReponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeInsight(params),
    queryFn: () => getInsightRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};

export const useGetRealtimeWifiSpeedtest = (
  params: RealtimeSpeedtestParams,
  options?: AxonQueryOptions<BFFResponseDTO<WifiStatisticsParameterResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeWifiSpeedtest(params),
    queryFn: () => getWifiSpeedtestRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};
