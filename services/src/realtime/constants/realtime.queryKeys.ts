import { ENDPOINTS } from './realtime.endpoint';
import { RealtimeParams, RealtimeInsightParams, RealtimeSpeedtestParams } from '../types/realtime.type';

export const QUERY_KEYS = {
  requestRealtimeId: (params: RealtimeParams) => [ENDPOINTS.REALTIME_REQUEST_ID, params],
  realtimeHealthcheck: (params: RealtimeParams) => [ENDPOINTS.REALTIME_HEALTH_CHECK, params],
  realtimeInsight: (params: RealtimeInsightParams) => [ENDPOINTS.REALTIME_INSIGHT, params],
  realtimeWifiSpeedtest: (params: RealtimeSpeedtestParams) => [ENDPOINTS.REALTIME_WIFI_SPEED_TEST, params],
  realtimeStatus: (params: RealtimeParams) => [ENDPOINTS.REALTIME_STATUS, params],
};
