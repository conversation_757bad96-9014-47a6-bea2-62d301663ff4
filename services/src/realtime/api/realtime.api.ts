import api from '@/api/apiService';
import { IHealthCheck } from '@/healthCheck/types/healthCheck.types';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/realtime.endpoint';
import {
  IRealtimeRequestIdRes,
  RealtimeParams,
  IRealtimeStatus,
  RealtimeInsightParams,
  RealtimeSpeedtestParams,
} from '../types/realtime.type';
import pollingRequest from '@/utils/polling.util';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';
import { WifiStatisticsParameterResponse } from '@/wifiStatistics/types/WifiStatistics';

export const getRealtimeDataRequestId = async (
  params: RealtimeParams,
): Promise<BFFResponseDTO<IRealtimeRequestIdRes>> => {
  const response = await api.get(ENDPOINTS.REALTIME_REQUEST_ID, { params });
  return response.data;
};

export const getRealtimeDataStatus = async (
  params: Required<RealtimeParams>,
  callback: (config: IRealtimeStatus) => void,
): Promise<BFFResponseDTO<IRealtimeStatus>> => {
  const response = await pollingRequest<IRealtimeStatus>(
    {
      url: ENDPOINTS.REALTIME_STATUS,
      params,
    },
    {
      endCondition: (data: IRealtimeStatus) => {
        callback?.(data);
        return get(data, 'realtimeRequestFinished');
      },
    },
  );
  if (!response.data) {
    throw new Error('Healthcheck request failed');
  }
  return response.data;
};

export const getHealthcheckRealtime = async (
  params: Required<RealtimeParams>,
): Promise<BFFResponseDTO<IHealthCheck>> => {
  const response = await api.get(ENDPOINTS.REALTIME_HEALTH_CHECK, { params });

  return response.data;
};

export const getInsightRealtime = async (params: RealtimeInsightParams): Promise<BFFResponseDTO<InsightReponse>> => {
  const response = await api.get(ENDPOINTS.REALTIME_INSIGHT, { params });

  return response.data;
};

export const getWifiSpeedtestRealtime = async (
  params: RealtimeSpeedtestParams,
): Promise<BFFResponseDTO<WifiStatisticsParameterResponse>> => {
  const response = await api.get(ENDPOINTS.REALTIME_WIFI_SPEED_TEST, { params });

  return response.data;
};
