{"since": "seit", "noStatusData": "<PERSON><PERSON> verfügbar", "deviceImageAlt": "Gerätebild", "deviceInfo": {"seeCPEStats": "CPE-Statistiken anzeigen", "cellularModem": "Mobilfunkmodem", "action": {"actionButton": "Aktion", "confirmText": "Bestätigen", "cancelText": "Abbrechen", "dialogDescription": "Sind <PERSON> sicher, dass Sie diese Aktion auf dem Gerät „{{deviceId}}“ durchführen möchten?", "cpe": {"title": "CPE", "deviceReboot": "Gerät neu starten", "environmentReboot": "Umfeld neu starten", "autoRebootConfig": "Auto-Neustart-Konfiguration", "factoryReset": "Werkseinstellungen zurücksetzen", "parentalControlReset": "Kindersicherung zurücksetzen", "tr069ProcessRestart": "TR069-<PERSON><PERSON><PERSON> neu starten", "firmwareUpdate": {"title": "Firmware-Update", "dialog": {"title": "Firmware aktualisieren ({{deviceId}})", "description": "Wählen Sie die neue Firmware zur Installation aus.", "currentVersion": "Aktuelle Version", "availableVersion": "Verfügbare Version", "updateText": "Update", "confirmUpdateText": "Möchten Sie die Firmware wirklich aktualisieren?"}}, "cpeConfigBackup": "CPE-Konfigurations-Backup", "cpeConfigRestore": "CPE-Konfigurations-Wiederherstellung", "downloadDeviceLog": "Ger<PERSON>ep<PERSON><PERSON><PERSON>"}, "wifi": {"title": "WIFI", "wifiConfig": "WI-FI-Konfiguration", "optimizeWifiChannels": "Wi-Fi-Kanäle optimieren", "restartWifiDriver": "Wi-Fi-Treiber neu starten", "restartWifiInterface": {"title": "Wi-Fi-Schnittstelle neu starten", "dialog": {"title": "Wi-Fi-Schnittstelle neu starten ({{deviceId}})", "description": "Wählen Sie das Band zum Neustart aus.", "wifiBand": "Wi-Fi-Band", "restartButton": "<PERSON>eu starten", "restartTitle": "Wi-Fi-Schnittstelle neu starten ({{deviceId}})", "restartConfirmText": "Sind <PERSON> sicher, dass Sie diese Schnittstelle zurücksetzen möchten?"}}}, "wan": {"title": "WAN", "restartWanInterface": "WAN-Schnittstelle neu starten", "restartPpp": "PPP neu starten", "pushPppConfig": {"title": "PPP-Konfiguration übertragen", "dialog": {"title": "PPP-Konfiguration übertragen ({{deviceId}})", "description": "Geben Sie Optionen für die Übertragung der PPP-Konfiguration ein.", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "protocolType": "Protokolltyp", "pppoe": "PPPoE (Standard)", "serviceName": "Dienstname (Optional)", "serviceNamePlaceHolder": "Dienstname", "mtuSize": "MTU-Größe (Optional)", "mtuSizePlaceHolder": "MTU", "authMethod": "Authentifizierungsmethode", "pap": "PAP (auto)", "connectionMode": "<PERSON>er<PERSON><PERSON>ng<PERSON><PERSON>", "alwaysOn": "Immer an", "ipMode": "IP-Modus", "dynamic": "<PERSON><PERSON><PERSON><PERSON> (Standard)"}}}}, "detail": {"cellularModem": {"currentType": "Aktueller Typ", "supportedType": "Unterstützter Typ"}, "deviceInfo": {"details": {"moreDetails": "Mehr Details", "deviceDetails": "G<PERSON><PERSON><PERSON><PERSON>", "seeStatistics": "Statistiken anzeigen", "interface": "Schnittstelle", "type": "<PERSON><PERSON>", "id": "ID", "id2": "ID #2", "model": "<PERSON><PERSON>", "firmware": "Firmware", "agentVer": "Agenten-Version", "publicIp": "Öffentliche IP", "lanIp": "LAN-IP", "mgmtIp": "Mgmt IP", "ipv6": "IPv6", "macAddress": "MAC-Adresse", "lastReboot": "Letzter Neustart", "lastUpload": "Letzter Upload", "configurationGroups": "Konfigurationsgruppen"}}}, "cpeStats": {"title": "CPE-Statistiken -", "searchInputPlaceholder": "<PERSON><PERSON>", "table": {"header": {"type": "<PERSON><PERSON>", "minMax": "Min-<PERSON>", "average": "Durchschnitt", "latestResult": "<PERSON><PERSON><PERSON><PERSON>", "noOfTests": "<PERSON><PERSON><PERSON>"}, "body": {"warning": "<PERSON><PERSON><PERSON>", "tooHigh": "zu hoch"}}}}, "cpe": {"title": "CPE", "cpeStats": {"title": "CPE-Statistiken"}, "cpeHistory": {"title": "CPE<PERSON><PERSON><PERSON><PERSON><PERSON>", "metric": {"title": "<PERSON><PERSON>", "qoe": {"title": "QoE", "yLabel": "AP-Gezondheid QoE"}, "cpuAndMemory": {"title": "CPU- und Speichernutzung", "cpuUsage": "CPU-Auslastung (%)", "cpuLoad": "CPU-Last", "freeMemory": "<PERSON><PERSON><PERSON> (MB)", "cpuUsageTooltip": "CPU-Auslastung", "cpuLoadTooltip": "CPU-Last", "freeMemoryTooltip": "<PERSON><PERSON><PERSON>"}, "chipsetTemp": {"title": "Temperatur", "yLabel": "Temperatur ({{unit}})"}, "powerCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rssi": "RSSI", "disconnectionEvent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "lanWlan": {"title": "LAN/WLAN", "wifiStats": {"title": "Wi-Fi-Statistiken", "selectBand": "Band auswählen", "runSpeedTest": {"button": "Geschwindigkeitstest durchführen", "modal": {"title": "Wi-Fi-Geschwindigkeitstest", "description": "<PERSON><PERSON><PERSON>en Sie die Parameter aus, die Si<PERSON> testen möchten.", "speed": "Geschwindigkeit", "latency": "<PERSON><PERSON>", "jitter": "Jitter", "congestion": "Überlastung"}}}, "lanPorts": "LAN-Ports", "wifiBands": "Wi-Fi-<PERSON><PERSON><PERSON>"}, "lanWLANHistory": {"title": "LAN/WLAN-<PERSON><PERSON><PERSON><PERSON>", "selectBand": "Band auswählen", "wifiQoE": {"wifiQoE": "Wi-Fi QoE", "channel": "<PERSON><PERSON>", "connectivity": "Konnektivität", "rssi": "RSSI", "speed": "Wi-Fi-Geschwindigkeit", "latency": "Wi-Fi-Latenz", "jitter": "Wi-Fi-Jitter", "transmissionErrors": "Wi-Fi-Übertragungsfehler", "channelNoise": "Wi-Fi-Ka<PERSON><PERSON>chen", "chipsetTemperature": "Wi-Fi-Chipsatztemperatur"}}, "wanHistory": {"options": {"stateOnlineOffline": "Status (online/offline)", "packetLost": "Paketverlust", "internetUsage": "Internetnutzung", "stability": "Stabilität", "speed": "Geschwindigkeit", "latency": "<PERSON><PERSON>", "jitter": "Jitter", "traffic": "Datenverkehr", "lteCellId": "Neuester LTE Cell-ID-Wert", "lteBand": "Neuester LTE-Band-Wert", "5gBand": "Neuester 5G-Band-Wert", "voiceRedundancy": "Neuester Sprachredundanzwert"}, "chart": {"detection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "up": "Upload-Verkehr", "down": "Download-Verkehr", "latency": "<PERSON><PERSON>", "packetLost": "Paketverlust", "internetUsage": "Internetnutzung", "jitterFirst": "Jitter0", "jitterSecond": "Jitter1", "packetLostFirst": "Paketverlust0", "packetLostSecond": "Paketverlust1", "cellId": "Cell Id", "band": "Band", "voiceRedundancy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "clientHistory": {"title": "Client<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectBand": "Band auswählen", "selectBandPlacehoder": "Wähle eine Band", "wifiThroughput": {"selectMetric": "<PERSON><PERSON>", "qoe": "QoE", "wifiPhyRate": "Wi-Fi Phy-Rate", "wifiThroughput": "Wi-Fi-Durchsatz", "latency": "<PERSON><PERSON>", "trafficDown": "Datenverkehr Downlink", "trafficUp": "Datenverkehr Uplink", "signalToNoiseRatio": "Signal-Rausch-Verhältnis", "rssiLevel": "RSSI-Pegel"}, "chart": {"up": "Upload-Verkehr", "down": "Download-Verkehr", "latency": "<PERSON><PERSON>", "jitterFirst": "Jitter0", "jitterSecond": "Jitter1"}}, "cellularModem": {"title": "Mobilfunkmodem", "settings": {"title": "Einstellungen", "general": "Allgemein", "roaming": "Wandernd", "autoSelectNetwork": "Netzwerk automatisch auswählen", "requestedNetwork": "Angefordertes Netzwerk", "tmobile": "T-Mobile", "vodafone": "Vodafone", "gigaclear": "G<PERSON><PERSON><PERSON>", "simPinCheckMethod": "SIM-Pin-Pro-Prothese", "onNetworkAccess": "Bei Netzwerkzugriff", "changeSimPin": "SIM-PIN <PERSON>"}, "moreDetails": {"title": "<PERSON><PERSON><PERSON> Details", "details": "Details", "interface": "Schnittstelle"}, "helpDrawer": {"title": "Was ist ein Mobilfunkmodem?", "description": "Das Mobilfunkmodem-Widget zeigt den aktuellen Betriebskanal zusammen mit der aktuellen Betriebsbandbreite an. Eine höhere Bandbreite ist in Abwesenheit einer starken externen Kanalauslastung typischerweise wünschenswert.", "descriptionRSSI": "Empfangssignalstärkeindikator: Der Träger-RSSI (Empfangssignalstärkeindikator) misst die durchschnittliche Gesamtempfangsleistung, die nur in OFDM-Symbolen beobachtet wird, die Referenzsymbole für Antennenport 0 enthalten (d.h. OFDM-Symbol 0 & 4 in einem Slot) in der Messbandbreite über N Ressourcenblöcke. Die Gesamtempfangsleistung des Träger-RSSI umfasst die Leistung von Co-Channel-Serving- und Non-Serving-Zellen, Nachbarkanalinterferenzen, thermisches Rauschen usw. Insgesamt gemessen über 12 Subträger einschließlich RS von der Serving-Zelle, Verkehr in der Serving-Zelle.", "descriptionRSRP": "Referenzsignal-Empfangsleistung: RSRP ist eine Art RSSI-Messung, wie folgt gibt es einige Definitionen und einige Details dazu. Es ist die Leistung der LTE-Referenzsignale, die über die gesamte Bandbreite und Schmalband verteilt sind. Ein Minimum von -20 dB SINR (des S-Synch-Kanals) ist erforderlich, um RSRP/RSRQ zu erkennen.", "descriptionRSRQ": "Referenzsignal-Empfangsqualität: Qualität unter Berücksichtigung von RSSI und der Anzahl der verwendeten Ressourcenblöcke (N) RSRQ = (N * RSRP) / RSSI gemessen über dieselbe Bandbreite. RSRQ ist eine C/I-Art von Messung und zeigt die Qualität des empfangenen Referenzsignals an. Die RSRQ-Messung liefert zusätzliche Informationen, wenn RSRP nicht ausreicht, um eine zuverlässige Übergabe- oder Zellneuauswahlentscheidung zu treffen."}}, "wanStatistics": {"searchInputPlaceholder": "<PERSON><PERSON>", "table": {"header": {"type": "<PERSON><PERSON>", "minMax": "Min-<PERSON>", "average": "Durchschnitt", "latestResult": "<PERSON><PERSON><PERSON><PERSON>", "noOfTests": "<PERSON><PERSON><PERSON>"}}}, "downlink": "Downlink-Datenverkehr", "uplink": "Uplink-Datenverkehr", "broadbandConnectivity": "Breitband-Konnektivität", "wifiQoe": "Wi-Fi QoE", "broadbandQoe": "Breitband-QoE", "temperature": "Temperatur"}