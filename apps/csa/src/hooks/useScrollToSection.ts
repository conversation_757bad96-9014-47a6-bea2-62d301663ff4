import { CPESection } from '@/constants/cpeElements';
import { TabConfig } from '@/stores/tab.store';
import { useEffect } from 'react';
import { flushSync } from 'react-dom';

export type SectionState = {
  showCpe: boolean;
  showLanWLan: boolean;
  showClientWidgets: boolean;
  showWanWidgets: boolean;
  showServicesWidget: boolean;
};

/**
 * Handling scroll-to-section functionality on device page
 * @param scrollConfig - The scroll configuration from tab store
 * @param openOnlySection - Open one section and close the others
 * @param addConfig - Function to update tab configuration
 * @param status - Device status
 */
export const useScrollToSection = (
  scrollConfig: TabConfig['scrollTo'],
  openOnlySection: (section: CPESection) => void,
  addConfigEnhance: (path: string, value: any, customerId?: string) => void,
  status: string | undefined,
) => {
  useEffect(() => {
    // We need to wait for the status online first
    // Because when it's online, it will change the toggle status of sections anyway
    if (!scrollConfig || status !== 'Online') return;

    const { section } = scrollConfig;

    // Update state synchronously to ensure DOM is ready for scrolling
    if (section) {
      flushSync(() => {
        openOnlySection(section);
      });
    }

    // Determine scroll target
    const scrollToId = scrollConfig.widget || section;
    if (!scrollToId) return;

    // Since we are using state change, then we use 2 requestAnimationFrame to ensure the DOM is updated before scrolling
    const frameId = requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        const nodeToScrollTo = document.getElementById(scrollToId);
        if (!nodeToScrollTo) return;

        const mainContent = document.getElementById('main-content');

        if (mainContent) {
          // Calculate scroll position with header offset 116px
          const top = nodeToScrollTo.getBoundingClientRect().top + mainContent.scrollTop - 72; // 56 is the header height + 16px offset
          mainContent.scrollTo({ top, behavior: 'smooth' });
        } else {
          // Fallback to standard scrollIntoView
          nodeToScrollTo.scrollIntoView({ behavior: 'smooth' });
        }

        // Clear scroll configuration after scrolling to prevent scrolling on browser refresh
        const timeoutId = setTimeout(() => {
          addConfigEnhance('scrollTo', { widget: '', section: '' });
        }, 100);

        // Cleanup timeout on unmount
        return () => clearTimeout(timeoutId);
      });
    });

    // Cleanup animation frame on unmount
    return () => cancelAnimationFrame(frameId);
  }, [scrollConfig, openOnlySection, addConfigEnhance, status]);
};
