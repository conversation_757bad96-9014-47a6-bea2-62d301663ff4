import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useGetCpeInfo } from 'services/CPEService';
import { useGetLineInfo } from 'services/LineInfo';
import { setTimezone } from 'services/Utils';
import get from 'lodash/get';

export const useSyncTimeZone = () => {
  return useQuery({
    queryKey: ['timezone'],
    queryFn: () => {
      return;
    },
  });
};
export const useUpdateTimeZone = () => {
  const queryClient = useQueryClient();

  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const type = useTabType();

  const { data: lineInfo } = useGetLineInfo(lineId || '', {
    enabled: type === 'customer' && !!lineId,
  });

  const { data: cpeInfo } = useGetCpeInfo(deviceId || '', {
    enabled: type === 'device' && !!deviceId,
  });

  const timezone = get(lineInfo, 'data.isp.timezone') || get(cpeInfo, 'data.timezone');

  useEffect(() => {
    if (timezone) {
      setTimezone(timezone);
      queryClient.setQueryData(['timezone'], timezone);
    }
  }, [timezone, queryClient]);
};
