import useTabStore, {
  useConfigDrawer,
  useConfigHealthCheck,
  useConfigNetworkTopology,
  useConfigScrollTo,
  useTabDeviceId,
  useTabId,
  useTabLineId,
} from '@/stores/tab.store';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

const sliceToNumber = (slice: string): number | null => {
  const map: Record<string, number> = {
    wan: 0,
    wlan: 1,
    clients: 2,
    services: 3,
    cpe: 4,
  };
  return map[slice] ?? null;
};

const sliceToName = (index: number): string => {
  const list = ['wan', 'wlan', 'clients', 'services', 'cpe'];
  return list[index] ?? '';
};

export const useTabViewSync = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const enabled = false || process.env.NODE_ENV === 'production';

  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const tabId = useTabId();
  const addTab = useTabStore((state) => state.addTab);
  const setActiveTab = useTabStore((state) => state.setActiveTab);
  const clearTabs = useTabStore((state) => state.clearTabs);
  const setIsHydrated = useTabStore((state) => state.setIsHydrated);
  const isHydrated = useTabStore((state) => state.isHydrated);
  const healthCheckConfig = useConfigHealthCheck();
  const scrollTo = useConfigScrollTo();
  const networkTopology = useConfigNetworkTopology();
  const drawer = useConfigDrawer();

  useEffect(() => {
    if (!enabled) return;

    const customerId = searchParams.get('customerId');
    const deviceId = searchParams.get('deviceId');
    const slice = searchParams.get('slice');
    const drawer = searchParams.get('drawer');
    const topologyOpen = searchParams.get('topologyOpen');

    if (!customerId) {
      setIsHydrated(true);
      return;
    }

    const id = deviceId || customerId;
    const type = deviceId ? 'device' : 'customer';

    addTab({
      id,
      name: id,
      type,
      customerId,
      deviceId: deviceId || null,
      config: {
        networkTopology: topologyOpen ? { open: true } : undefined,
        healthCheck: slice ? { selectedSlice: sliceToNumber(slice) } : undefined,
        drawer,
      },
    });

    setActiveTab({ tabId: id, tabType: type });
    setIsHydrated(true);
  }, [searchParams, addTab, setActiveTab, clearTabs, setIsHydrated, enabled]);

  useEffect(() => {
    if (!isHydrated || !enabled) return;

    if (!tabId) {
      navigate('/csa', { replace: true });
      return;
    }

    const params = new URLSearchParams();

    if (lineId) params.set('customerId', lineId);
    if (deviceId) params.set('deviceId', deviceId);

    const slice = healthCheckConfig?.selectedSlice;
    if (slice !== undefined && slice !== null) {
      params.set('slice', sliceToName(slice));
    }

    if (drawer) params.set('drawer', drawer);

    const topologyOpen = networkTopology?.open;
    if (topologyOpen) params.set('topologyOpen', 'true');

    const newQuery = params.toString();
    if (window.location.search !== `?${newQuery}`) {
      navigate(`/csa?${newQuery}`, { replace: true });
    }
  }, [scrollTo, healthCheckConfig, isHydrated, navigate, enabled, lineId, deviceId, tabId, networkTopology, drawer]);
};
