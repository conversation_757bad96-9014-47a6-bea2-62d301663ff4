import { AxonAreaChart, AxonButton, AxonCard, AxonCardContent, AxonSeparator } from 'ui/UIComponents';
import { toRoundedDecimalNumber } from '@/utils/number';
import { cn } from '@/utils';
import { <PERSON>Down, <PERSON>U<PERSON>, Minus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';

interface Props {
  name: string;
  unit: string;
  points: Array<{ value: number; pct: number; timestamp: number }>;
}

export const InitialScreenReport = ({ name, unit, points }: Props) => {
  const { t } = useTranslation();

  const sortedResults = useMemo(() => {
    const uniqueTimestamps = Array.from(new Set(points.map((p) => p.timestamp)));

    return uniqueTimestamps.sort().map((timestamp) => {
      const filteredPoints = points.filter((p) => p.timestamp === timestamp);
      // Kaan: some of the value will have the same timestamp, we need to unify them
      const sumValues = filteredPoints.reduce((acc, curr) => acc + curr.value, 0);
      const sumPcts = filteredPoints.reduce((acc, curr) => acc + curr.pct, 0);

      return {
        timestamp,
        value: unit === '%' ? sumPcts : sumValues,
        number: sumValues,
        pct: sumPcts,
      };
    });
  }, [points, unit]);

  const latestValue = useMemo(() => sortedResults[sortedResults.length - 1]?.value, [sortedResults]);

  const changedInPercentage = useMemo(() => {
    const latestValue = sortedResults[sortedResults.length - 1]?.value;

    if (!latestValue) return 0;

    const allExceptLatest = sortedResults.slice(0, sortedResults.length - 1);
    const average = allExceptLatest.reduce((acc, curr) => acc + curr.value, 0) / allExceptLatest.length;

    return ((latestValue - average) * 100) / average;
  }, [sortedResults]);

  return (
    <AxonCard radialGradientTypes={['success']}>
      <AxonCardContent className='flex h-full flex-col justify-end p-5'>
        <div className='flex flex-1 flex-row items-start gap-x-2'>
          <div className='flex h-full w-1/2 flex-col justify-between gap-y-1'>
            <p className='text-content-primary font-book text-sm opacity-60'>{t(`welcomeScreen:${name}`)}</p>
            <p className='text-2xl font-medium'>
              {toRoundedDecimalNumber(latestValue, 5)}
              {unit}
            </p>
          </div>
          <div className='flex h-full w-1/2 items-center'>
            <AxonAreaChart
              className='aspect-auto h-16 w-full'
              chartData={sortedResults}
              color='green'
              valueDataKey='value'
              tooltip={{
                formatter: (_a, _b, item) => {
                  return `${item.payload.number} - ${toRoundedDecimalNumber(item.payload.pct, 5)}%`;
                },
              }}
            />
          </div>
        </div>
        <AxonSeparator className='my-3' />
        <div className='flex flex-row flex-wrap items-center gap-x-2 overflow-auto'>
          <AxonButton
            variant='secondary'
            size='icon-xs'
            className={cn({
              'bg-content-meta-red/20': unit === '%' ? changedInPercentage > 0 : changedInPercentage < 0,
              'bg-content-meta-green/20': unit === '%' ? changedInPercentage < 0 : changedInPercentage > 0,
            })}>
            {changedInPercentage > 0 ? (
              <ArrowUp size={12} className={cn(unit === '%' ? 'text-content-meta-red' : 'text-content-meta-green')} />
            ) : changedInPercentage < 0 ? (
              <ArrowDown size={12} className={cn(unit === '%' ? 'text-content-meta-green' : 'text-content-meta-red')} />
            ) : (
              <Minus size={12} />
            )}
          </AxonButton>
          <p
            className={cn(`text-xs font-medium`, {
              'text-content-meta-red': unit === '%' ? changedInPercentage > 0 : changedInPercentage < 0,
              'text-content-meta-green': unit === '%' ? changedInPercentage < 0 : changedInPercentage > 0,
              'text-content-primary': changedInPercentage === 0,
            })}>
            {changedInPercentage !== 0 ? toRoundedDecimalNumber(changedInPercentage, 2) : null}
            {changedInPercentage ? '%' : ''} {changedInPercentage === 0 && t('translation:home.noChange')}
          </p>
          <p className='font-book text-xs opacity-60'>{t('translation:home.vsLastPeriod')}</p>
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};
