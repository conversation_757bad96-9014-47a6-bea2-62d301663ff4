import { useStartRealtimeProcess } from 'services/Realtime';
import { useGetLineInfo } from 'services/LineInfo';
import get from 'lodash/get';
import useTabStore, { EUnit, useConfigRealtime, useTabLineId, useTimeRangeSelected } from '@/stores/tab.store';
import { useShallow } from 'zustand/react/shallow';
import { getDayjsFromToday, getDayjsFromTodayByDays } from 'services/Utils';
import { useTranslation } from 'react-i18next';

export const useCustomerAction = () => {
  const { addConfigEnhance, setTimeRange } = useTabStore(
    useShallow((state) => ({
      addConfigEnhance: state.addConfigEnhance,
      setTimeRange: state.setTimeRange,
    })),
  );

  const { t } = useTranslation();
  const lineId = useTabLineId() || '';
  const realtimeConfig = useConfigRealtime();
  const timeRangeSelected = useTimeRangeSelected();
  const { data: lineInfo, isLoading: isLoadingCustomerInfo, isError: isErrorCustomerInfo } = useGetLineInfo(lineId);
  const { startRealtimeProcess } = useStartRealtimeProcess();
  const enableRealtimeRequesting = get(realtimeConfig, 'enableRealtimeRequesting', false);
  const timeRange = get(timeRangeSelected, 'unit');
  const activeProbing = get(realtimeConfig, 'activeProbing', false);
  const currentStepText = enableRealtimeRequesting
    ? get(realtimeConfig, 'currentStepText') || t('processing...')
    : t('getRealtimeData');

  const enableCollectRealtimeData = get(lineInfo, 'data.network.config.enableRealtimeCollectData', false);
  const disabledRealtime = isErrorCustomerInfo || isLoadingCustomerInfo || !enableCollectRealtimeData;

  const handleGetRealtime = () => {
    if (timeRange === EUnit.CUSTOM) {
      setTimeRange({
        unit: EUnit.THREE_DAYS,
        startDate: getDayjsFromTodayByDays(EUnit.THREE_DAYS)?.toDate() || null,
        endDate: getDayjsFromToday(0)?.toDate() || null,
      });
    }
    addConfigEnhance('realtime.enableRealtimeRequesting', true, lineId);
    addConfigEnhance('realtime.currentStepId', null, lineId);
    addConfigEnhance('realtime.currentStepText', null, lineId);
    startRealtimeProcess(
      {
        customerId: lineId,
        activeProbing,
      },
      addConfigEnhance,
    );
  };

  const handleSetActiveProbing = (value: boolean) => {
    addConfigEnhance('realtime.activeProbing', value);
  };

  const handleCancel = () => {
    addConfigEnhance('realtime.activeProbing', false);
  };
  return {
    handleGetRealtime,
    isLoading: enableRealtimeRequesting,
    activeProbing,
    handleSetActiveProbing,
    currentStepText,
    handleCancel,
    disabledRealtime,
  };
};
