import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuLabel,
  AxonDropdownMenuSeparator,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';

import { useTabDeviceId } from '@/stores/tab.store';
import { useState } from 'react';
import {
  ArrowUpCircle,
  Command,
  Download,
  DownloadCloud,
  Power,
  RotateCcw,
  Settings,
  Shuffle,
  UploadCloud,
} from 'ui/UIAssets';
import DeviceRebootDialog from './MenuItemDialogs/DeviceRebootDialog';
import FactoryResetDialog from './MenuItemDialogs/FactoryResetDialog';
import OptimizeWifiChannelsDialog from './MenuItemDialogs/OptimizeWifiChannelsDialog';
import RestartPppDialog from './MenuItemDialogs/RestartPppDialog';
import RestartWanInterfaceDialog from './MenuItemDialogs/RestartWanInterfaceDialog';
import Tr069ProcessRestart from './MenuItemDialogs/Tr069ProcessRestartDialog';
import PushPppConfigDialog from './MenuItemDialogs/PushPppConfigDialog';
import FirmwareUpdateDialog from './MenuItemDialogs/FirmwareUpdateDialog';
import RestartWifiInterfaceDialog from './MenuItemDialogs/RestartWifiInterfaceDialog';
import { useTranslation } from 'react-i18next';
import EnvironmentRebootDialog from './MenuItemDialogs/EnvironmenRebootDialog';

const ActionMenu = () => {
  const { t } = useTranslation();
  const deviceId = useTabDeviceId();
  const [dialogsState, setDialogsState] = useState({
    // CPE
    deviceRebootDialog: false,
    environmentRebootDialog: false,
    factoryResetDialog: false,
    tr069ProcessRestartDialog: false,
    firmwareUpdateDialog: false,

    // WIFI
    wifiConfigurationDialog: false,
    optimizeWifiChannelsDialog: false,
    restartWifiInterfaceDialog: false,

    // WAN
    restartWanInterfaceDialog: false,
    restartPppDialog: false,
    pushPppConfigDialog: false,
  });

  return (
    <>
      <AxonDropdownMenu>
        <AxonDropdownMenuTrigger asChild>
          <AxonButton variant='primary' disabled>
            <Command className='mr-2 size-4' />
            {t('device:deviceInfo.action.actionButton')}
          </AxonButton>
        </AxonDropdownMenuTrigger>
        <AxonDropdownMenuContent className='w-2xs'>
          <AxonDropdownMenuLabel className='text-content-tertiary'>
            {t('device:deviceInfo.action.cpe.title')}
          </AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, deviceRebootDialog: true })}>
            <Power className='size-4' /> {t('device:deviceInfo.action.cpe.deviceReboot')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, environmentRebootDialog: true })}>
            <Power className='size-4' />
            {t('device:deviceInfo.action.cpe.environmentReboot')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <Settings className='size-4' />
            {t('device:deviceInfo.action.cpe.autoRebootConfig')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, factoryResetDialog: true })}>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.cpe.factoryReset')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.cpe.parentalControlReset')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, tr069ProcessRestartDialog: true })}>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.cpe.tr069ProcessRestart')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, firmwareUpdateDialog: true })}>
            <ArrowUpCircle className='size-4' />
            {t('device:deviceInfo.action.cpe.firmwareUpdate.title')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <DownloadCloud className='size-4' />
            {t('device:deviceInfo.action.cpe.cpeConfigBackup')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <UploadCloud className='size-4' />
            {t('device:deviceInfo.action.cpe.cpeConfigRestore')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <Download className='size-4' />
            {t('device:deviceInfo.action.cpe.downloadDeviceLog')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuSeparator />

          <AxonDropdownMenuLabel className='text-content-tertiary'>
            {t('device:deviceInfo.action.wifi.title')}
          </AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, wifiConfigurationDialog: true })}>
            <Settings className='size-4' />
            {t('device:deviceInfo.action.wifi.wifiConfig')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, optimizeWifiChannelsDialog: true })}>
            <Shuffle className='size-4' />
            {t('device:deviceInfo.action.wifi.optimizeWifiChannels')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.wifi.restartWifiDriver')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartWifiInterfaceDialog: true })}>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.wifi.restartWifiInterface.title')}
          </AxonDropdownMenuItem>

          <AxonDropdownMenuSeparator />
          <AxonDropdownMenuLabel className='text-content-tertiary'>
            {t('device:deviceInfo.action.wan.title')}
          </AxonDropdownMenuLabel>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartWanInterfaceDialog: true })}>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.wan.restartWanInterface')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, restartPppDialog: true })}>
            <RotateCcw className='size-4' />
            {t('device:deviceInfo.action.wan.restartPpp')}
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem onClick={() => setDialogsState({ ...dialogsState, pushPppConfigDialog: true })}>
            <ArrowUpCircle className='size-4' />
            {t('device:deviceInfo.action.wan.pushPppConfig.title')}
          </AxonDropdownMenuItem>
        </AxonDropdownMenuContent>
      </AxonDropdownMenu>
      {/* CPE */}
      {deviceId && (
        <DeviceRebootDialog
          open={dialogsState.deviceRebootDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, deviceRebootDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <EnvironmentRebootDialog
          open={dialogsState.environmentRebootDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, environmentRebootDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <FactoryResetDialog
          open={dialogsState.factoryResetDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, factoryResetDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <Tr069ProcessRestart
          open={dialogsState.tr069ProcessRestartDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, tr069ProcessRestartDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <FirmwareUpdateDialog
          open={dialogsState.firmwareUpdateDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, firmwareUpdateDialog: open })}
          deviceId={deviceId}
        />
      )}

      {/* WIFI */}
      {deviceId && (
        <OptimizeWifiChannelsDialog
          open={dialogsState.optimizeWifiChannelsDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, optimizeWifiChannelsDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <RestartWifiInterfaceDialog
          open={dialogsState.restartWifiInterfaceDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartWifiInterfaceDialog: open })}
          deviceId={deviceId}
        />
      )}

      {/* WAN */}
      {deviceId && (
        <RestartWanInterfaceDialog
          open={dialogsState.restartWanInterfaceDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartWanInterfaceDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <RestartPppDialog
          open={dialogsState.restartPppDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, restartPppDialog: open })}
          deviceId={deviceId}
        />
      )}
      {deviceId && (
        <PushPppConfigDialog
          open={dialogsState.pushPppConfigDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, pushPppConfigDialog: open })}
          deviceId={deviceId}
        />
      )}
    </>
  );
};

export default ActionMenu;
