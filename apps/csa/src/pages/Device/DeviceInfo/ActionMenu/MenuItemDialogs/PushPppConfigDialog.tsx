import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const PushPppConfigDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const { t } = useTranslation();
  return (
    <AxonAlertDialog open={open} onOpenChange={onOpenChange}>
      <AxonAlertDialogContent className='min-w-[640px] gap-0'>
        <AxonAlertDialogHeader>
          <AxonAlertDialogTitle></AxonAlertDialogTitle>
          <AxonAlertDialogDescription></AxonAlertDialogDescription>
        </AxonAlertDialogHeader>
        <div className='flex flex-col gap-y-6'>
          <p className='text-content-primary text-lg font-medium'>
            {t('device:deviceInfo.action.wan.pushPppConfig.dialog.title', { deviceId })}
          </p>
          <p className='text-content-secondary text-md'>
            {t('device:deviceInfo.action.wan.pushPppConfig.dialog.description')}
          </p>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='username'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.username')}
              </AxonLabel>
              <AxonInput id='username' placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.username')} />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='password'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.password')}
              </AxonLabel>
              <AxonInput
                type='password'
                id='password'
                placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.password')}
              />
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='protocolType'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.protocolType')}
              </AxonLabel>
              <AxonSelect defaultValue='pppoe'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.protocolType')} />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='pppoe'>
                    {t('device:deviceInfo.action.wan.pushPppConfig.dialog.pppoe')}
                  </AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='serviceName'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.serviceName')}
              </AxonLabel>
              <AxonInput
                type='serviceName'
                id='serviceName'
                placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.serviceNamePlaceHolder')}
              />
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='mtu'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.mtuSize')}
              </AxonLabel>
              <AxonInput
                id='mtu'
                placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.mtuSizePlaceHolder')}
                value={1492}
              />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='authenticationMethod'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.authMethod')}
              </AxonLabel>
              <AxonSelect defaultValue='pap'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.authMethod')} />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='pap'>
                    {t('device:deviceInfo.action.wan.pushPppConfig.dialog.pap')}
                  </AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>
          <div className='flex flex-row items-center gap-x-3'>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='connectionMode'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.connectionMode')}
              </AxonLabel>
              <AxonSelect defaultValue='alwaysOn'>
                <AxonSelectTrigger>
                  <AxonSelectValue
                    placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.connectionMode')}
                  />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='alwaysOn'>
                    {t('device:deviceInfo.action.wan.pushPppConfig.dialog.alwaysOn')}
                  </AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='ipMode'>
                {t('device:deviceInfo.action.wan.pushPppConfig.dialog.ipMode')}
              </AxonLabel>
              <AxonSelect defaultValue='Dynamic'>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder={t('device:deviceInfo.action.wan.pushPppConfig.dialog.ipMode')} />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  <AxonSelectItem value='Dynamic'>
                    {t('device:deviceInfo.action.wan.pushPppConfig.dialog.dynamic')}
                  </AxonSelectItem>
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>
        </div>
        <AxonAlertDialogFooter className='mt-10'>
          <AxonAlertDialogCancel>{t('device:deviceInfo.action.cancelText')}</AxonAlertDialogCancel>
          <AxonAlertDialogAction>{t('device:deviceInfo.action.confirmText')}</AxonAlertDialogAction>
        </AxonAlertDialogFooter>
      </AxonAlertDialogContent>
    </AxonAlertDialog>
  );
};

export default PushPppConfigDialog;
