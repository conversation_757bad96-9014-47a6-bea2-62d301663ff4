import { useState } from 'react';
import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const RestartWifiInterfaceDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const { t } = useTranslation();
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [selectedBand, setSelectedBand] = useState('');
  const wifibands = ['2.4 GHz', '5 GHz', '6 GHz'];

  return (
    <>
      <AxonAlertDialog open={open} onOpenChange={onOpenChange} key={deviceId}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>
              {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.title', { deviceId })}
            </p>
            <p className='text-content-secondary text-md'>
              {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.description')}
            </p>

            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.wifiBand')}
              </AxonLabel>
              <AxonSelect value={selectedBand} onValueChange={setSelectedBand}>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Wi-Fi Band' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  {wifibands.map((version) => (
                    <AxonSelectItem key={version} value={version}>
                      {version}
                    </AxonSelectItem>
                  ))}
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>Cancel</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedBand} onClick={() => setOpenConfirmDialog(true)}>
              {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.restartButton')}
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>

      <AxonAlertDialog open={openConfirmDialog} onOpenChange={setOpenConfirmDialog}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>
              {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.restartTitle', { deviceId })}
            </p>
            <p className='text-content-secondary text-md'>
              {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.restartConfirmText')}
            </p>

            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='wifiBand'>
                {t('device:deviceInfo.action.wifi.restartWifiInterface.dialog.wifiBand')}
              </AxonLabel>
              <AxonInput value={selectedBand} disabled />
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>{t('device:deviceInfo.action.cancelText')}</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedBand} onClick={() => setOpenConfirmDialog(true)}>
              {t('device:deviceInfo.action.confirmText')}
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
    </>
  );
};

export default RestartWifiInterfaceDialog;
