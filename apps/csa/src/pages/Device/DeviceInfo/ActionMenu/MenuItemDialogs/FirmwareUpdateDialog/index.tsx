import { useState } from 'react';
import {
  AxonAlertDialog,
  AxonAlertDialogAction,
  AxonAlertDialogCancel,
  AxonAlertDialogContent,
  AxonAlertDialogDescription,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonInput,
  AxonLabel,
  AxonSelect,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};

const FirmwareUpdateDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const { t } = useTranslation();
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [selectedVersion, setSelectedVersion] = useState('');
  const currentVersion = '1.0.7.213241';
  const availableVersions = ['1.0.11.213892', '1.0.10.213892', '1.0.9.213892', '1.0.8.213892', '1.0.7.213892'];

  return (
    <>
      <AxonAlertDialog open={open} onOpenChange={onOpenChange} key={deviceId}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>
              {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.title', { deviceId })}
            </p>
            <p className='text-content-secondary text-md'>
              {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.description')}
            </p>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='currentVersion'>
                {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.currentVersion')}
              </AxonLabel>
              <AxonInput id='currentVersion' placeholder='Current Version' value={currentVersion} disabled />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.availableVersion')}
              </AxonLabel>
              <AxonSelect value={selectedVersion} onValueChange={setSelectedVersion}>
                <AxonSelectTrigger>
                  <AxonSelectValue placeholder='Available Version' />
                </AxonSelectTrigger>
                <AxonSelectContent>
                  {availableVersions.map((version) => (
                    <AxonSelectItem key={version} value={version}>
                      {version}
                    </AxonSelectItem>
                  ))}
                </AxonSelectContent>
              </AxonSelect>
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>{t('device:deviceInfo.action.cancelText')}</AxonAlertDialogCancel>
            <AxonAlertDialogAction disabled={!selectedVersion} onClick={() => setOpenConfirmDialog(true)}>
              {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.updateText')}
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>

      <AxonAlertDialog open={openConfirmDialog} onOpenChange={setOpenConfirmDialog}>
        <AxonAlertDialogContent className='min-w-[350px] gap-0'>
          <AxonAlertDialogHeader>
            <AxonAlertDialogTitle></AxonAlertDialogTitle>
            <AxonAlertDialogDescription></AxonAlertDialogDescription>
          </AxonAlertDialogHeader>
          <div className='flex flex-col gap-y-6'>
            <p className='text-content-primary text-lg font-medium'>
              {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.title', { deviceId })}
            </p>
            <p className='text-content-secondary text-md'>
              {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.confirmUpdateText')}
            </p>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='currentVersion'>
                {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.currentVersion')}
              </AxonLabel>
              <AxonInput id='currentVersion' placeholder='Current Version' value={currentVersion} disabled />
            </div>
            <div className='flex flex-1 flex-col gap-y-3'>
              <AxonLabel className='font-book text-content-secondary text-sm' htmlFor='availableVersion'>
                {t('device:deviceInfo.action.cpe.firmwareUpdate.dialog.availableVersion')}
              </AxonLabel>
              <AxonInput id='availableVersion' placeholder='Available Version' value={selectedVersion} disabled />
            </div>
          </div>

          <AxonAlertDialogFooter className='mt-6'>
            <AxonAlertDialogCancel>{t('device:deviceInfo.action.cancelText')}</AxonAlertDialogCancel>
            <AxonAlertDialogAction>{t('device:deviceInfo.action.confirmText')}</AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
    </>
  );
};

export default FirmwareUpdateDialog;
