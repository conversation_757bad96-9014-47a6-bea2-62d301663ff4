import { AxonAlertDialogWrapper } from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
};
const EnvironmentRebootDialog = ({ deviceId, onOpenChange, open }: Props) => {
  const { t } = useTranslation();
  return (
    <AxonAlertDialogWrapper
      open={open}
      onOpenChange={onOpenChange}
      title={t('device:deviceInfo.action.cpe.environmentReboot')}
      description={t('device:deviceInfo.action.dialogDescription', { deviceId })}
      confirmText={t('device:deviceInfo.action.confirmText')}
      cancelText={t('device:deviceInfo.action.cancelText')}
      onConfirm={() => {}}
    />
  );
};

export default EnvironmentRebootDialog;
