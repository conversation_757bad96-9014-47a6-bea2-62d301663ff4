import { useGetCpeHistory, useGetCpeInfo } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';
import useTabStore, { useConfigWidgetCpeHistory, useTabDeviceId, useTimeRangeSelected } from '@/stores/tab.store';
import { useMemo } from 'react';
import get from 'lodash/get';
import { TCpeHistoryDataPoint } from './type';
import isEmpty from 'lodash/isEmpty';
import i18n from '@/i18n';
import { CpeHistoryChartMetric } from '@/stores/widgets.config';

const t = i18n.t;

const lineChartConfig = {
  [CpeHistoryChartMetric.CPU_MEMORY_USAGE]: {
    yAxisProps: [
      {
        yAxisId: 'cpuUsageLeft',
        label: {
          value: t('device:cpe.cpeHistory.metric.cpuAndMemory.cpuUsage'),
          dx: -20,
          angle: -90,
        },
        domain: [0, 100],
        width: 50,
      },
      {
        yAxisId: 'cpuLoadLeft',
        label: {
          value: t('device:cpe.cpeHistory.metric.cpuAndMemory.cpuLoad'),
          dx: -20,
          angle: -90,
        },
        domain: [0, 2],
        width: 50,
      },
      {
        yAxisId: 'freeMemoryRight',
        label: {
          value: t('device:cpe.cpeHistory.metric.cpuAndMemory.freeMemory'),
          dx: 20,
          angle: 90,
        },
        orientation: 'right' as const,
      },
    ],
    linesProps: [
      {
        dataKey: 'cpuUsage',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'cpuUsageLeft',
        name: t('device:cpe.cpeHistory.metric.cpuAndMemory.cpuUsageTooltip'),
      },
      {
        dataKey: 'cpuLoad',
        color: 'rgb(var(--chart-2))',
        yAxisId: 'cpuLoadLeft',
        name: t('device:cpe.cpeHistory.metric.cpuAndMemory.cpuLoadTooltip'),
      },
      {
        dataKey: 'freeMemory',
        color: 'rgb(var(--chart-3))',
        yAxisId: 'freeMemoryRight',
        name: t('device:cpe.cpeHistory.metric.cpuAndMemory.freeMemoryTooltip'),
      },
    ],
  },
  [CpeHistoryChartMetric.CHIPSET_TEMPERATURE]: {
    yAxisProps: [
      {
        yAxisId: 'chipsetTemperature',
        label: {
          value: t('device:cpe.cpeHistory.metric.chipsetTemp.yLabel', { unit: '°C' }),
          dx: -20,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'chipsetTemperature',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'chipsetTemperature',
        name: t('device:cpe.cpeHistory.metric.chipsetTemp.title'),
      },
    ],
  },
  [CpeHistoryChartMetric.POWER_CYCLE]: {
    yAxisProps: [
      {
        yAxisId: 'powerCycle',
        allowDecimals: false,
        label: {
          value: t('device:cpe.cpeHistory.metric.powerCycle'),
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'powerCycle',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'powerCycle',
        name: t('device:cpe.cpeHistory.metric.powerCycle'),
        connectNulls: true,
      },
    ],
  },
  [CpeHistoryChartMetric.QOE]: {
    yAxisProps: [
      {
        yAxisId: 'qoe',
        label: {
          value: t('device:cpe.cpeHistory.metric.qoe.yLabel'),
          dx: get(i18n, 'language') === 'de' ? -70 : -50,
          angle: -90,
        },
      },
    ],
    linesProps: [
      {
        dataKey: 'qoe',
        color: 'rgb(var(--chart-1))',
        yAxisId: 'qoe',
        name: t('device:cpe.cpeHistory.metric.qoe.title'),
        connectNulls: true,
      },
    ],
  },
  [CpeHistoryChartMetric.RSSI]: {
    yAxisProps: [
      {
        label: {
          value: t('device:cpe.cpeHistory.metric.rssi'),
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [] as any,
  },
  [CpeHistoryChartMetric.DISCONNECTION_EVENT]: {
    yAxisProps: [
      {
        allowDecimals: false,
        label: {
          value: t('device:cpe.cpeHistory.metric.disconnectionEvent'),
          dx: -30,
          angle: -90,
        },
      },
    ],
    linesProps: [] as any,
  },
};

const METRIC_FOR_EXTENDER = [CpeHistoryChartMetric.RSSI, CpeHistoryChartMetric.DISCONNECTION_EVENT];

function generateConfig(metric: CpeHistoryChartMetric, listLines: Record<string, { key: string }>) {
  const config = lineChartConfig[metric];
  if (METRIC_FOR_EXTENDER.includes(metric) && !isEmpty(listLines)) {
    config.linesProps = Object.values(listLines).map((line, index) => {
      return {
        ...line,
        dataKey: line.key,
        color: `rgb(var(--chart-${(index % 30) + 1}))`,
      };
    });
  }
  return config;
}

export const useCpeHistoryAction = () => {
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const deviceId = useTabDeviceId() || '';
  const timeRangeSelected = useTimeRangeSelected();
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const {
    data: cpeInfo,
    isLoading: isCpeInfoLoading,
    isError: isCpeInfoError,
  } = useGetCpeInfo(deviceId, {
    enabled: !!deviceId,
    staleTime: Infinity,
  });
  const lineId = get(cpeInfo, 'data.customerId') ?? ''; // id of account
  const deviceType = get(cpeInfo, 'data.deviceType') ?? '';
  const isExtender = deviceType === 'Extender';
  const selectedMetricType = useConfigWidgetCpeHistory()!.selectedMetric;

  const {
    data,
    isLoading: isCpeHistoryLoading,
    isError: isCpeHistoryError,
  } = useGetCpeHistory(
    {
      customerId: lineId,
      deviceId: deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!lineId && !!deviceId && !!startDate,
    },
  );

  const chartData = useMemo(() => {
    const results = get(data, `data.datapoints`) ?? [];
    const listLines = {};
    let datapoints = results;

    if (METRIC_FOR_EXTENDER.includes(selectedMetricType)) {
      datapoints = [];
      let hasData = false;
      (results as TCpeHistoryDataPoint[]).forEach((point) => {
        const { date, ...rest } = point;
        const metricValue = get(rest, `${selectedMetricType}`) || {};
        const data = {
          date,
        };
        Object.entries(metricValue).forEach(([parentDeviceId, bands]) => {
          Object.entries(bands).forEach(([bandId, datapointValue]) => {
            const bandName = `${bandId}(${parentDeviceId})`;
            if (datapointValue || datapointValue === 0) {
              // if datapointValue is not null or 0, then it has data
              hasData = true;
            }
            listLines[bandName] = {
              key: bandName,
            };
            data[bandName] = datapointValue;
          });
        });
        datapoints.push(data);
      });
      if (!hasData) {
        datapoints = []; // if no data, return empty array
      }
    }

    if (selectedMetricType === CpeHistoryChartMetric.QOE) {
      datapoints = (results as TCpeHistoryDataPoint[]).filter((point) => point.qoe !== null);
    } else if (selectedMetricType === CpeHistoryChartMetric.POWER_CYCLE) {
      datapoints = (results as TCpeHistoryDataPoint[]).filter((point) => point.powerCycle !== null);
    }
    return {
      config: generateConfig(selectedMetricType, listLines),
      data: datapoints,
    };
  }, [selectedMetricType, data]);

  const handleChangeMetric = (metric: CpeHistoryChartMetric) => {
    addConfigEnhance('widgets.cpeHistory.selectedMetric', metric);
  };

  return {
    selectedMetricType,
    handleChangeMetric,
    chartData,
    isLoading: isCpeInfoLoading || isCpeHistoryLoading,
    isError: isCpeInfoError || isCpeHistoryError,
    isExtender,
  };
};
