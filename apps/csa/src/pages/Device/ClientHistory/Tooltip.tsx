import { AxonChartTooltipContent, TooltipIcon } from 'ui/UIComponents';
import { cn } from '@/utils';
import { formatDate, DATETIME_FORMAT } from 'services/Utils';
import get from 'lodash/get';

export function ClientHistoryTooltip(props: any) {
  const { payload } = props;
  const labelFormatter = (_, payload) => {
    return formatDate(get(payload, '0.payload.date'), DATETIME_FORMAT.DATE_TIME);
  };
  const formatter = (_: number | string | Array<number | string>, name: string | number, props: any) => {
    const dataKey = props.dataKey;
    const match = dataKey.match(/^(.+)\.([^.]+)\.([^.]+)$/);
    if (!match) {
      return null;
    }
    const id = match[1];
    const band = match[2];
    const rxKey = `${id}.${band}.rxPhyRate`;
    const txKey = `${id}.${band}.txPhyRate`;
    const rxValue = props.payload[rxKey];
    const txValue = props.payload[txKey];
    const valueFormat = `Rx ${rxValue}Mbps / Tx ${txValue}Mbps`;

    return (
      <div className='flex items-center gap-2'>
        <TooltipIcon color={props.color} indicator='dot' nestLabel={false} />
        <div className={cn('flex flex-1 items-center justify-between leading-none')}>
          <div className={cn('mr-2 grid gap-1.5')}>
            <span className='text-muted-foreground'>{name}:</span>
          </div>
          {valueFormat && (
            <span className={cn('text-foreground font-mono font-medium tabular-nums')}>{valueFormat}</span>
          )}
        </div>
      </div>
    );
  };
  if (payload.length > 0) {
    return (
      <AxonChartTooltipContent
        {...props}
        formatter={formatter}
        labelFormatter={labelFormatter}
        uniquePayloadKey='name'
      />
    );
  }
  return null;
}
