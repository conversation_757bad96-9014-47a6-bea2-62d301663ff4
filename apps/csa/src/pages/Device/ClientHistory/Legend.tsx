export interface IPayload {
  id?: string;
  value: string;
  color: string;
  type?: string;
}

interface IProps {
  payload: IPayload[];
}

export function ClientHistoryLegend(props: IProps) {
  const { payload } = props;
  const phyRatePayload: IPayload[] = [];
  const otherPayload: IPayload[] = [];
  payload.forEach((entry) => {
    if (entry.id === 'phyrate-tx' || entry.id === 'phyrate-rx') {
      phyRatePayload.push(entry);
    } else {
      otherPayload.push(entry);
    }
  });

  function getIcon(entry: IPayload) {
    if (entry.type === 'dashed') {
      return (
        <div className='flex justify-between gap-1'>
          <div className='bg-error-500 size-1' />
          <div className='bg-error-500 h-1 w-2' />
          <div className='bg-error-500 size-1' />
        </div>
      );
    } else if (entry.type === 'solid') {
      return <div className='bg-error-500 h-1 w-6' />;
    }
    return <div className='size-2 rounded-full' style={{ backgroundColor: entry.color }} />;
  }

  return (
    <div className='flex justify-between'>
      <ul className='flex basis-1/4 flex-wrap gap-2'>
        {phyRatePayload.map((entry, index) => (
          <li key={`item-${index}`} className='flex items-center gap-2'>
            {getIcon(entry)}
            <span>{entry.value}</span>
          </li>
        ))}
      </ul>
      <ul className='flex flex-wrap gap-4'>
        {otherPayload.map((entry, index) => (
          <li key={`item-${index}`} className='flex items-center gap-2'>
            <div className='size-3 rounded-full' style={{ backgroundColor: entry.color }} />
            <span>{entry.value}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
