import useTabStore, { useConfigWidgetClientHistory, useTabDeviceId, useTimeRangeSelected } from '@/stores/tab.store';
import { useGetCpeInfo } from 'services/CPEService';
import { getDayjsFromUnixTime, getUnixTime, getTimezone } from 'services/Utils';
import { useGetClientHistory } from 'services/Client';
import get from 'lodash/get';
import { useMemo, useEffect } from 'react';
import { CpeClient } from './type';
import isEmpty from 'lodash/isEmpty';
import { ClientHistoryEMetric } from '@/stores/widgets.config';
import uniqBy from 'lodash/uniqBy';
import { ClientHistoryLegend, IPayload } from './Legend';
import { ClientHistoryTooltip } from './Tooltip';

export const ETHERNET_METRICS = [
  ClientHistoryEMetric.TRAFFIC_DOWN,
  ClientHistoryEMetric.TRAFFIC_UP,
  ClientHistoryEMetric.LATENCY,
  ClientHistoryEMetric.QOE,
];

const lineChartConfig = {
  [ClientHistoryEMetric.WIFI_THROUGHPUT]: {
    yAxisProps: [
      {
        label: {
          value: 'Mbps',
          dx: -20,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.LATENCY]: {
    yAxisProps: [
      {
        label: {
          value: 'ms',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.RSSI]: {
    yAxisProps: [
      {
        label: {
          value: 'dBm',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.SNR]: {
    yAxisProps: [
      {
        label: {
          value: 'dB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.TRAFFIC_DOWN]: {
    yAxisProps: [
      {
        label: {
          value: 'MB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.TRAFFIC_UP]: {
    yAxisProps: [
      {
        label: {
          value: 'MB',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.WIFI_PHY_RATE]: {
    yAxisProps: [
      {
        label: {
          value: 'Mbps',
          dx: -40,
          angle: -90,
        },
      },
    ],
  },
  [ClientHistoryEMetric.QOE]: {
    yAxisProps: [
      {
        label: {
          value: 'QoE',
          dx: -50,
          angle: -90,
        },
      },
    ],
  },
};

function getClientName(client: CpeClient) {
  return client.name ? `${client.name} (${client.mac})` : client.mac;
}

function generateConfig(
  metric: ClientHistoryEMetric,
  listOfKeys: { key: string; name: string; lineDash?: string; color?: string; isPhyRate?: boolean }[],
) {
  const result = {
    linesProps: listOfKeys.map(({ key, name, lineDash, color }, i) => {
      return {
        dataKey: key,
        color: color || `rgb(var(--chart-${(i % 30) + 1}))`, // max 30 variables colors predefined in global.css
        name: name,
        connectNulls: metric === ClientHistoryEMetric.QOE || metric === ClientHistoryEMetric.WIFI_PHY_RATE,
        strokeDasharray: lineDash,
      };
    }),
    yAxisProps: lineChartConfig[metric].yAxisProps,
    legendProps: {},
    tooltipProps: {},
  };

  if (metric === ClientHistoryEMetric.WIFI_PHY_RATE) {
    const payload: IPayload[] = [];
    const uniquePayload = uniqBy(listOfKeys, 'name');
    uniquePayload.forEach(({ name, color }, i) => {
      payload.push({
        id: name,
        value: name,
        color: color || `rgb(var(--chart-${(i % 30) + 1}))`, // max 30 variables colors predefined in global.css
      });
    });
    payload.push({
      id: 'phyrate-rx',
      value: 'Rx',
      type: 'solid',
      color: 'rgb(var(--error-500))',
    });
    payload.push({
      id: 'phyrate-tx',
      value: 'Tx',
      type: 'dashed',
      color: 'rgb(var(--error-500))',
    });
    result.legendProps = {
      payload,
      content: ClientHistoryLegend,
    };
    result.tooltipProps = {
      content: ClientHistoryTooltip,
    };
  }

  return result;
}

export const useClientHistoryAction = () => {
  const deviceId = useTabDeviceId() || '';
  const timeRangeSelected = useTimeRangeSelected();
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const { data: cpeInfo, isError: isCpeInfoError } = useGetCpeInfo(deviceId, {
    enabled: !!deviceId,
    staleTime: Infinity,
  });

  const lineId = get(cpeInfo, 'data.customerId') ?? '';
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const clientHistoryConfig = useConfigWidgetClientHistory()!;
  const { filterState } = clientHistoryConfig;

  const {
    data,
    isError: isCpeHistoryError,
    ...rest
  } = useGetClientHistory(
    {
      customerId: lineId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!lineId && !!deviceId && !!startDate,
    },
  );

  const chartData = useMemo(() => {
    const { client: filteredClient, metric, band: bandFiltered } = filterState;
    const points = get(data, 'data.datapoints') ?? [];
    const formatData: Record<string, unknown>[] = [];
    const listOfKeys = {};
    if (points && filteredClient && filteredClient.length > 0) {
      points.forEach((point) => {
        const { date, stations } = point;
        const listDataForClients = {};
        filteredClient.forEach((client) => {
          const bandId = bandFiltered?.bandId || '';
          if (metric === ClientHistoryEMetric.WIFI_THROUGHPUT && !client.throughputBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.LATENCY && !client.latencyBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.RSSI && !client.rssiBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.SNR && !client.snrBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.TRAFFIC_DOWN && !client.trafficDownBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.TRAFFIC_UP && !client.trafficUpBandAvailable[bandId]) {
            return;
          }
          if (metric === ClientHistoryEMetric.QOE && !client.qoeBandAvailable) {
            return;
          }
          if (
            metric === ClientHistoryEMetric.WIFI_PHY_RATE &&
            !client.txPhyRateBandAvailable[bandId] &&
            !client.rxPhyRateBandAvailable[bandId]
          ) {
            return;
          }

          let key = `${client.mac}.${bandId}.${metric}`;
          if (metric === ClientHistoryEMetric.QOE) {
            key = `${client.mac}.qoe`;
          }
          if (metric === ClientHistoryEMetric.WIFI_PHY_RATE) {
            const txPhyRateKey = `${client.mac}.${bandId}.txPhyRate`;
            const rxPhyRateKey = `${client.mac}.${bandId}.rxPhyRate`;
            const txPhyRatePoint = get(stations, txPhyRateKey) as unknown as number;
            const rxPhyRatePoint = get(stations, rxPhyRateKey) as unknown as number;
            listDataForClients[txPhyRateKey] = txPhyRatePoint;
            listDataForClients[rxPhyRateKey] = rxPhyRatePoint;
            if (!listOfKeys[txPhyRateKey]) {
              const indexOfKey = Object.keys(listOfKeys).length;
              listOfKeys[txPhyRateKey] = {
                key: txPhyRateKey,
                name: getClientName(client),
                lineDash: '5, 5',
                isPhyRate: true,
                color: `rgb(var(--chart-${(indexOfKey % 30) + 1}))`,
              };
              listOfKeys[rxPhyRateKey] = {
                key: rxPhyRateKey,
                name: getClientName(client),
                isPhyRate: true,
                color: `rgb(var(--chart-${(indexOfKey % 30) + 1}))`,
              };
            }
          } else {
            const metricPointValue = get(stations, key) as unknown as number;
            listDataForClients[key] = metricPointValue;
            if (!listOfKeys[key]) {
              listOfKeys[key] = {
                key,
                name: getClientName(client),
              };
            }
          }
        });

        if (!isEmpty(listDataForClients)) {
          if (metric === ClientHistoryEMetric.QOE) {
            // only push on start of the day to remove hour from now
            const day = getDayjsFromUnixTime(date)?.tz(getTimezone());
            const isStartOfDay = day?.get('hour') === 0;
            if (isStartOfDay) {
              formatData.push({
                date,
                ...listDataForClients,
              });
            }
          } else {
            formatData.push({
              date,
              ...listDataForClients,
            });
          }
        }
      });
    }
    return {
      config: generateConfig(metric, Object.values(listOfKeys)),
      data: formatData,
    };
  }, [filterState, data]);
  const clientList = useMemo<CpeClient[]>(() => {
    const clientData = get(data, 'data.clients') ?? {};
    return Object.values(clientData);
  }, [data]);

  useEffect(() => {
    if (data) {
      const bands = get(data, 'data.bands') ?? [];
      const clientList = get(data, 'data.clients') ?? [];
      if (clientList.length > 0 && !get(clientHistoryConfig, 'filterState.client')) {
        addConfigEnhance('widgets.clientHistory.filterState.client', clientList);
      }

      if (bands.length > 0) {
        const selectedBand = get(clientHistoryConfig, 'filterState.band') ?? bands[0];
        addConfigEnhance('widgets.clientHistory.filterState.band', selectedBand);
      }
    }
  }, [data, addConfigEnhance, clientHistoryConfig]);

  const bands = get(data, 'data.bands') ?? [];

  return {
    ...rest,
    isError: isCpeInfoError || isCpeHistoryError,
    data,
    filter: filterState,
    setFilter: addConfigEnhance,
    chartData,
    clientList,
    bands,
  };
};
