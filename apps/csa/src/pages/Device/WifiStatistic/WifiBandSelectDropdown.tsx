import {
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectItem,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
interface Props {
  disabled?: boolean;
  wifiBands: Array<{ id: string; title: string }>;
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
}

export const WifiBandSelectDropdown = ({ disabled, wifiBands, selectedBandId, setSelectedBandId }: Props) => {
  const { t } = useTranslation();
  useEffect(() => {
    if (wifiBands.length === 0) return;
    const isExist = wifiBands.find(({ id }) => id === selectedBandId);
    if (!selectedBandId || !isExist) {
      const firstBand = wifiBands[0];
      if (firstBand?.id) {
        setSelectedBandId(firstBand.id);
      }
    }
  }, [selectedBandId, setSelectedBandId, wifiBands]);

  return (
    <AxonSelect disabled={disabled} value={selectedBandId} onValueChange={setSelectedBandId}>
      <AxonSelectTrigger
        className='w-fit gap-x-2 border-none bg-transparent'
        aria-label={t('device:lanWlan.wifiStats.selectBand')}>
        <AxonSelectValue placeholder={t('device:lanWlan.wifiStats.selectBand')} />
      </AxonSelectTrigger>
      <AxonSelectContent>
        <AxonSelectGroup>
          <AxonSelectLabel>{t('device:lanWlan.wifiStats.selectBand')}</AxonSelectLabel>
          {wifiBands.map((b) => {
            return (
              <AxonSelectItem key={`${b.title}-${b.id}`} value={b.id}>
                {b.title}
              </AxonSelectItem>
            );
          })}
        </AxonSelectGroup>
      </AxonSelectContent>
    </AxonSelect>
  );
};
