import { StatisticWidgetStatus } from '@/components/SharedStatisticComponents/StatisticWidgetStatus';
import { useWifiSpeedtestAction } from '@/pages/Device/WifiStatistic/useWifiSpeedtestAction';
import { WifiBandSelectDropdown } from '@/pages/Device/WifiStatistic/WifiBandSelectDropdown';
import useTabStore, {
  useConfigRealtime,
  useConfigWidgetWifiStatistic,
  useTabDeviceId,
  useTabLineId,
  useTimeRangeSelected,
} from '@/stores/tab.store';
import get from 'lodash/get';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetWifiBands } from 'services/CPEService';
import { useGetLineInfo } from 'services/LineInfo';
import { useGetRealtimeWifiSpeedtest } from 'services/Realtime';
import { getUnixTime } from 'services/Utils';
import { useGetWifiStatisticsParameter } from 'services/WifiStatistics';
import { Loading, RefreshCcw } from 'ui/UIAssets';
import { AxonButton, AxonCard, AxonCardContent, AxonSeparator } from 'ui/UIComponents';
import Error from './Error';
import RunSpeedTestModal from './Modals/runSpeedTest';
import Skeleton from './Skeleton';
import { WarnTooltip } from './WarnTooltip';
import WifiStatisticDrawer from './WifiStatisticDrawer';
import { cn } from '@/utils';

const WifiStatistic = () => {
  const { t } = useTranslation();
  const [openRunSpeedTestModal, setOpenRunSpeedTestModal] = useState(false);
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const lineId = useTabLineId() || '';
  const deviceId = useTabDeviceId() || '';

  const selectedBandId = useConfigWidgetWifiStatistic()!.selectedBandId;
  const realtimeConfig = useConfigRealtime();
  const timeRangeSelected = useTimeRangeSelected();
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const realtimeRequestId = get(realtimeConfig, 'realtimeRequestId', '');
  const isRealtimeDataReady = get(realtimeConfig, 'dataIsReady', false);
  const isRealtimeRequestEnabled = get(realtimeConfig, 'enableRealtimeRequesting', false);

  const {
    data: lineInfo,
    isLoading: isLoadingCustomerInfo,
    isError: isErrorCustomerInfo,
  } = useGetLineInfo(lineId ?? '');

  const {
    handleGetRealtime,
    currentStepText: realtimeTestText,
    isLoading: isWifiSpeedtestRunning,
  } = useWifiSpeedtestAction();

  const {
    data: wifiBandsData,
    isLoading: isLoadingWifiBands,
    isError: isErrorWifiBands,
  } = useGetWifiBands({
    customerId: lineId,
    deviceId,
    startDate: getUnixTime(startDate) || 0,
    endDate: getUnixTime(endDate) || 0,
  });

  const {
    data: queryDataParameter,
    isLoading: isLoadingWifiStats,
    isError: isErrorParameter,
  } = useGetWifiStatisticsParameter(
    {
      customerId: lineId,
      deviceId,
      bandId: selectedBandId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    {
      enabled: !!lineId && !!deviceId && !!selectedBandId && !!startDate,
    },
  );

  const { data: realtimeWifiSpeedtestData, isFetching: isFetchingWifiSpeedtestRealtime } = useGetRealtimeWifiSpeedtest(
    {
      customerId: lineId,
      realtimeRequestId,
      deviceId,
      bandId: selectedBandId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    {
      enabled: isRealtimeDataReady && !!lineId && !!deviceId && !!selectedBandId && !!startDate,
    },
  );

  const isRequestingRealtime = useMemo(
    () => isFetchingWifiSpeedtestRealtime || isRealtimeRequestEnabled,
    [isRealtimeRequestEnabled, isFetchingWifiSpeedtestRealtime],
  );

  const isCollectRealtimeDataEnabled = get(lineInfo, 'data.network.config.enableRealtimeCollectData', true);
  const isCollectRealtimeDataDisabled = useMemo(
    () => isErrorCustomerInfo || isLoadingCustomerInfo || !isCollectRealtimeDataEnabled,
    [isErrorCustomerInfo, isLoadingCustomerInfo, isCollectRealtimeDataEnabled],
  );

  const wifiStatParameters = useMemo(() => {
    return isRequestingRealtime ? undefined : realtimeWifiSpeedtestData?.data || queryDataParameter?.data;
  }, [isRequestingRealtime, queryDataParameter?.data, realtimeWifiSpeedtestData?.data]);

  const mappedWifiBands = useMemo(() => {
    return wifiBandsData?.data?.results.map((b) => {
      return {
        title: b.frequency,
        id: b.bandId,
      };
    });
  }, [wifiBandsData]);

  function handleChangeOpenDialog(status: boolean) {
    setOpenRunSpeedTestModal(status);
  }

  const handleSubmitDataCheck = () => {
    setOpenRunSpeedTestModal(false);
    handleGetRealtime();
  };

  const isLoading = useMemo(
    () => isLoadingWifiBands || isLoadingWifiStats || isRequestingRealtime || isFetchingWifiSpeedtestRealtime,
    [isLoadingWifiBands, isFetchingWifiSpeedtestRealtime, isLoadingWifiStats, isRequestingRealtime],
  );

  const handleChangeBandId = (bandId: string) => {
    addConfigEnhance('widgets.wifiStatistic.selectedBandId', bandId);
  };

  return (
    <AxonCard
      className='bg-surface-tile border-gradient-border'
      radialGradientTypes={['success']}
      radialGradientPosition='left bottom'>
      <AxonCardContent className='p-0'>
        <div className='flex h-[361px] flex-col p-6'>
          <div className='flex flex-row items-center'>
            <p className='text-md text-content-primary font-semibold'>{t('device:lanWlan.wifiStats.title')}</p>
            <div className='ml-auto flex flex-row items-center gap-x-1'>
              <AxonButton
                startDecorator={
                  isWifiSpeedtestRunning ? (
                    <Loading className={cn('size-4 animate-spin')} />
                  ) : (
                    <RefreshCcw className='size-4' />
                  )
                }
                variant='outline'
                disabled={isCollectRealtimeDataDisabled || isWifiSpeedtestRunning}
                className='bg-surface-action text-component-hyperlink hover:text-component-hyperlink w-fit border-none shadow-none'
                onClick={() => setOpenRunSpeedTestModal(true)}>
                {realtimeTestText}
              </AxonButton>

              <RunSpeedTestModal
                open={openRunSpeedTestModal}
                onSubmitData={handleSubmitDataCheck}
                onOpenChange={handleChangeOpenDialog}
              />

              {Array.isArray(mappedWifiBands) && mappedWifiBands.length && (
                <WifiBandSelectDropdown
                  disabled={isLoading}
                  wifiBands={mappedWifiBands}
                  selectedBandId={selectedBandId}
                  setSelectedBandId={handleChangeBandId}
                />
              )}

              <WifiStatisticDrawer
                selectedBandId={selectedBandId}
                setSelectedBandId={handleChangeBandId}
                wifiBands={mappedWifiBands}
                customerId={lineId}
                deviceId={deviceId}
                startDate={startDate}
                endDate={endDate}
              />
            </div>
          </div>
          {isLoading ? (
            <Skeleton />
          ) : isErrorParameter || isErrorCustomerInfo || isErrorWifiBands ? (
            <Error />
          ) : (
            <>
              <div className='grid h-[206px] grid-cols-4'>
                {wifiStatParameters?.data?.map((item, i) => (
                  <div
                    key={i}
                    className={`flex flex-col gap-y-1 px-4 py-3 ${i % 4 === 0 ? 'pl-0' : ''} ${i % 4 !== 3 ? 'border-r-2' : ''} ${i < 4 ? 'border-b-2' : ''}`}>
                    <p className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'>
                      {item.title}
                    </p>
                    <div
                      className={`flex flex-row items-center gap-x-2 ${item.status && item.status === 'warn' ? 'text-content-meta-orange' : ''}`}>
                      <div className={`flex flex-wrap items-center gap-x-1 text-lg font-semibold`}>
                        <span>
                          {item.valueUp} {item.upUnit ?? ''}
                        </span>
                        {item.status && item.status === 'warn' ? <WarnTooltip message={item.warnMessage || ''} /> : ''}
                        {typeof item.valueDn === 'number' && (
                          <>
                            <span> / {item.valueDn}</span>
                            {item.status && item.status === 'warn' && <WarnTooltip message={item.warnMessage || ''} />}
                          </>
                        )}
                        {typeof item.valueUp === 'number' && <span>{item.unit}</span>}
                      </div>
                    </div>
                    {item.lastSeen && <p className='text-content-secondary text-xs font-medium'>{item.lastSeen}</p>}
                  </div>
                ))}
              </div>
              <AxonSeparator className='my-6' />
              <StatisticWidgetStatus
                qoeValue={wifiStatParameters?.status}
                lastUpdated={wifiStatParameters?.lastUpdated}
              />
            </>
          )}
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};

export default WifiStatistic;
