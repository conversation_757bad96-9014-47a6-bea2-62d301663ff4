import { CPEDrawers } from '@/constants/cpeElements';
import { WifiBandSelectDropdown } from '@/pages/Device/WifiStatistic/WifiBandSelectDropdown';
import useTabStore, {
  ETimeRange,
  EUnit,
  useConfigDrawer,
  useConfigWidgetWifiStatistic,
  useTimeRangeSelected,
} from '@/stores/tab.store';
import { convertQOEStatus } from '@/utils/QOE.util';
import { getCoreRowModel, getFilteredRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Maximize, Search } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getDayjsFromDate, getDayjsFromToday, getTimezone, getUnixTime } from 'services/Utils';
import { useGetWifiStatisticsTableDrawer } from 'services/WifiStatistics';
import {
  AxonButton,
  AxonDateRangePicker,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonTableData,
  AxonTableInputSearch,
} from 'ui/UIComponents';
import get from 'lodash/get';

interface Props {
  wifiBands?: Array<{ title: string; id: string }>;
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
  customerId?: string;
  deviceId?: string;
  startDate?: ETimeRange['startDate'];
  endDate?: ETimeRange['endDate'];
}

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const WifiStatisticDrawer = ({
  customerId,
  deviceId,
  startDate,
  endDate,
  wifiBands,
  selectedBandId,
  setSelectedBandId,
}: Props) => {
  const drawer = useConfigDrawer();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const { t } = useTranslation();
  const openDrawer = drawer === CPEDrawers.WIFI_STATISTICS;
  const handleDrawerOpenChange = (open: boolean) => {
    addConfigEnhance('drawer', open ? CPEDrawers.WIFI_STATISTICS : null);
  };
  if (!customerId || !deviceId || !startDate) {
    return null;
  }

  return (
    <AxonSheet open={openDrawer} onOpenChange={handleDrawerOpenChange}>
      <AxonSheetTrigger key='card-insights' asChild>
        <AxonButton variant='ghost' className='ml-4' aria-label={t('ariaLabel.openWifiStats')}>
          <Maximize size={16} className='cursor-pointer' />
        </AxonButton>
      </AxonSheetTrigger>
      <WifiStatsDrawerTable
        selectedBandId={selectedBandId}
        setSelectedBandId={setSelectedBandId}
        wifiBands={wifiBands || []}
        customerId={customerId}
        deviceId={deviceId}
        startDate={startDate}
        endDate={endDate || undefined}
      />
    </AxonSheet>
  );
};

interface WifiStatsDrawerTableProps {
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
  wifiBands: Array<{ title: string; id: string }>;
  customerId: string;
  deviceId: string;
  startDate: Date;
  endDate?: Date;
}

const WifiStatsDrawerTable = ({
  selectedBandId,
  setSelectedBandId,
  wifiBands,
  customerId,
  deviceId,
}: WifiStatsDrawerTableProps) => {
  const setTimeRange = useTabStore((state) => state.setTimeRange);
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const timeRangeSelected = useTimeRangeSelected();
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);
  const { t } = useTranslation();

  const {
    data: queryDataTableDrawer,
    isLoading,
    isError,
  } = useGetWifiStatisticsTableDrawer(
    {
      customerId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
      bandId: selectedBandId,
    },
    {
      enabled: !!customerId && !!deviceId && !!selectedBandId && !!startDate,
    },
  );

  const QOEStatus = queryDataTableDrawer?.data.QOEStatus;
  const queryParameters = queryDataTableDrawer?.data.parameters;

  const parameters = useMemo(() => queryParameters || [], [queryParameters]);

  const dataTable = useMemo(() => {
    if (QOEStatus) {
      return [
        {
          type: QOEStatus.type,
          minMax: '',
          average: '',
          latestResult: t(convertQOEStatus(QOEStatus.latestResult)),
          noOfTest: null,
        },
        ...parameters,
      ];
    }

    return parameters;
  }, [QOEStatus, parameters, t]);
  // const [globalFilter, setGlobalFilter] = useState('');
  const globalFilter = useConfigWidgetWifiStatistic()!.globalFilter;

  const columns = [
    { accessorKey: 'type', header: t('device:deviceInfo.cpeStats.table.header.type'), cell: (info) => info.getValue() },
    {
      accessorKey: 'minMax',
      header: t('device:deviceInfo.cpeStats.table.header.minMax'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'average',
      header: t('device:deviceInfo.cpeStats.table.header.average'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'latestResult',
      header: t('device:deviceInfo.cpeStats.table.header.latestResult'),
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: 'noOfTest',
      header: t('device:deviceInfo.cpeStats.table.header.noOfTests'),
      cell: (info) => info.getValue(),
    },
  ];

  function setGlobalFilter(value: string) {
    addConfigEnhance('widgets.wifiStatistic.globalFilter', value);
  }

  const table = useReactTable({
    data: dataTable,
    columns,
    state: {
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,

    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  function handleSetTimeRange(date?: DateRange) {
    const startDate = getDayjsFromDate(date?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(date?.to).startOf('day').tz(getTimezone(), true).toDate();

    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate,
      endDate,
    });
  }

  return (
    <AxonSheetContent className='insight-axonsheet-content md w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-md 2xl:max-w-screen-lg'>
      <div className='border-b-border-flat flex h-16 items-center border-b px-6'>
        <WifiBandSelectDropdown
          wifiBands={wifiBands.map((band) => ({
            ...band,
            title: `${t('device:lanWlan.wifiStats.title')} - ${band.title}`,
          }))}
          selectedBandId={selectedBandId}
          setSelectedBandId={setSelectedBandId}
        />
      </div>
      <div className='border-b-border-flat insight-search-block flex h-16 items-center justify-between border-b px-6'>
        <div className='relative h-fit'>
          <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />

          <AxonTableInputSearch
            value={globalFilter}
            onChange={(value) => table.setGlobalFilter(String(value))}
            placeholder={t('device:deviceInfo.cpeStats.searchInputPlaceholder')}
            className='h-8 w-[360px] pl-10'
          />
        </div>
        <div className='flex w-fit gap-2'>
          <AxonDateRangePicker
            selected={{
              from: startDate ?? undefined,
              to: endDate ?? undefined,
            }}
            onApply={handleSetTimeRange}
            showIcon={false}
            disabled={{
              after: new Date(),
              before: getDayjsFromToday(31, 'day')?.toDate(),
            }}
          />
        </div>
      </div>
      <div className='flex flex-wrap content-start items-start gap-3 self-stretch px-5'>
        <AxonTableData table={table} showFooter={false} isLoading={isLoading} isError={isError} />
      </div>
    </AxonSheetContent>
  );
};

export default WifiStatisticDrawer;
