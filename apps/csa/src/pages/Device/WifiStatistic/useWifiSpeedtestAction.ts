import { useStartRealtimeProcess } from 'services/Realtime';
import { useGetLineInfo } from 'services/LineInfo';
import get from 'lodash/get';
import useTabStore, { useConfigRealtime, useTabLineId } from '@/stores/tab.store';
import { useTranslation } from 'react-i18next';

export const useWifiSpeedtestAction = () => {
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const { t } = useTranslation();
  const activeLineId = useTabLineId() || '';
  const {
    data: lineInfo,
    isLoading: isLoadingCustomerInfo,
    isError: isErrorCustomerInfo,
  } = useGetLineInfo(activeLineId);
  const { startRealtimeProcess } = useStartRealtimeProcess();
  const realtimeConfig = useConfigRealtime();
  const enableRealtimeRequesting = get(realtimeConfig, 'enableRealtimeRequesting', false);
  const currentStepText = enableRealtimeRequesting
    ? get(realtimeConfig, 'currentStepText') || t('processing...')
    : t('device:lanWlan.wifiStats.runSpeedTest.button');

  const enableCollectRealtimeData = get(lineInfo, 'data.customer.config.enableRealtimeCollectData', false);
  const disabledRealtime = isErrorCustomerInfo || isLoadingCustomerInfo || !enableCollectRealtimeData;

  const handleGetRealtime = () => {
    addConfigEnhance('realtime.enableRealtimeRequesting', true);
    addConfigEnhance('realtime.currentStepId', null);
    addConfigEnhance('realtime.currentStepText', null);
    startRealtimeProcess(
      {
        customerId: activeLineId,
        isActiveWifiSpeedtest: true,
      },
      addConfigEnhance,
    );
  };

  return {
    handleGetRealtime,
    isLoading: enableRealtimeRequesting,
    currentStepText: currentStepText,
    disabledRealtime,
  };
};
