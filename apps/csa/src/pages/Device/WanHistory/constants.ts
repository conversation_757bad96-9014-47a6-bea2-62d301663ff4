import { BASE_WAN_HISTORY_METRICS, CELLULAR_WAN_HISTORY_METRICS } from '@/stores/widgets.config';

export const BASE_WAN_HISTORY_METRICS_OPTIONS = [
  { value: BASE_WAN_HISTORY_METRICS.STATE, label: 'stateOnlineOffline' },
  { value: BASE_WAN_HISTORY_METRICS.SPEED, label: 'speed' },
  { value: BASE_WAN_HISTORY_METRICS.LATENCY, label: 'latency' },
  { value: BASE_WAN_HISTORY_METRICS.JITTER, label: 'jitter' },
  { value: BASE_WAN_HISTORY_METRICS.TRAFFIC, label: 'traffic' },
  { value: BASE_WAN_HISTORY_METRICS.PACKET_LOST, label: 'packetLost' },
  { value: BASE_WAN_HISTORY_METRICS.INTERNET_USAGE, label: 'internetUsage' },
];

export const CELLULAR_WAN_HISTORY_METRICS_OPTIONS = [
  { value: CELLULAR_WAN_HISTORY_METRICS.LTE_CELL_ID, label: 'lteCellId' },
  { value: CELLULAR_WAN_HISTORY_METRICS.LTE_BAND, label: 'lteBand' },
  { value: CELLULAR_WAN_HISTORY_METRICS['5G_BAND'], label: '5gBand' },
  { value: CELLULAR_WAN_HISTORY_METRICS.VOICE_REDUNDANCY, label: 'voiceRedundancy' },
];
