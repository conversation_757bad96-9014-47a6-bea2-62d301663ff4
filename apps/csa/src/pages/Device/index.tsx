import Insights from '@/components/Insights';
import Logs from '@/components/Logs';
import CpeStatistics from '@/pages/Device/CpeStatistics';
import WanStatistics from '@/pages/Device/WanStatistics';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RefreshCcw, ZapOff } from 'ui/UIAssets';
import {
  AxonBadge,
  AxonButton,
  AxonCard,
  AxonDialog,
  AxonDialogContent,
  AxonDialogDescription,
  AxonDialogFooter,
  AxonDialogHeader,
  AxonDialogTitle,
} from 'ui/UIComponents';
import CellularModem from './CellularModem';
import ClientDevice from './ClientDevice';
import ClientHistory from './ClientHistory';
import CpeHistory from './CpeHistory';
import DeviceInfo from './DeviceInfo';
import LANPorts from './LANPorts';
import ServiceHistory from './ServiceHistory';
// import TimeRange from './TimeRange';
import TimeRange from '@/components/TimeRange';
import { CPESection, CPESections } from '@/constants/cpeElements';
import { SectionState, useScrollToSection } from '@/hooks/useScrollToSection';
import { useSyncTimeZone } from '@/hooks/useTimeZone';
import useTabStore, { useConfigScrollTo, useTabDeviceId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { scrollToElement } from '@/utils/scroll.utils';
import throttle from 'lodash/throttle';
import { useGetCpeInfo } from 'services/CPEService';
import { useRefetchAll } from 'services/Global';
import LanWlanHistory from './LanWlanHistory';
import WanConfig from './WanConfig';
import WanHistory from './WanHistory';
import WifiBands from './WifiBands';
import WifiStatistic from './WifiStatistic';

const Device = () => {
  useSyncTimeZone();
  const { t } = useTranslation();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const deviceId = useTabDeviceId();
  const scrollTo = useConfigScrollTo();

  const { data } = useGetCpeInfo(deviceId ?? '', { enabled: !!deviceId });
  const { refetchAll } = useRefetchAll();

  const [isShowDialogOfflineConfirmation, setIsShowDialogOfflineConfirmation] = useState(false);
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [state, setState] = useState<SectionState>({
    showCpe: true,
    showLanWLan: true,
    showClientWidgets: true,
    showWanWidgets: true,
    showServicesWidget: true,
  });

  const isDeviceOnline = data?.data?.status == 'Online';
  const isDeviceOffline = data?.data?.status === 'Offline';

  useEffect(() => {
    if (isDeviceOnline) {
      setState({
        showCpe: true,
        showLanWLan: true,
        showClientWidgets: true,
        showWanWidgets: true,
        showServicesWidget: true,
      });
      setIsShowDialogOfflineConfirmation(false);
      setIsOverlayVisible(false);
    }
    if (isDeviceOffline) {
      setState({
        showCpe: false,
        showLanWLan: false,
        showClientWidgets: false,
        showWanWidgets: false,
        showServicesWidget: false,
      });
      setIsShowDialogOfflineConfirmation(true);
      setIsOverlayVisible(true);
    }
  }, [isDeviceOnline, isDeviceOffline]);

  const openOnlySection = useCallback((section: CPESection) => {
    setState({
      showCpe: section === CPESections.CPE,
      showLanWLan: section === CPESections.LAN_WLAN,
      showClientWidgets: section === CPESections.CLIENTS,
      showWanWidgets: section === CPESections.WAN,
      showServicesWidget: section === CPESections.SERVICES,
    });
  }, []);

  // Use custom hook for scroll-to-section functionality
  useScrollToSection(scrollTo, openOnlySection, addConfigEnhance, data?.data?.status);

  function closeAndStay() {
    setIsOverlayVisible(true);
    setIsShowDialogOfflineConfirmation(false);
    setState({
      showCpe: false,
      showLanWLan: false,
      showClientWidgets: false,
      showWanWidgets: false,
      showServicesWidget: false,
    });
  }

  function goToMostRecent() {
    setIsOverlayVisible(false);
    setIsShowDialogOfflineConfirmation(false);
    setState({
      showCpe: true,
      showLanWLan: true,
      showClientWidgets: true,
      showWanWidgets: true,
      showServicesWidget: true,
    });
  }

  const handleClickBadge = (elementId: string) => {
    requestAnimationFrame(() => {
      scrollToElement(elementId);
    });
  };

  const styleForOverlay = {
    opacity: isOverlayVisible ? 0.25 : 1,
  };

  return (
    <div className='customer/page-device 3xl:w-[1280px] m-auto flex flex-col gap-y-6'>
      <DeviceInfo />
      <TimeRange />

      <div className='3xl:flex-row relative flex flex-col items-start gap-6'>
        <div className='health-check 3xl:min-w-[904px] w-full flex-[2]'>
          <div className='flex flex-col gap-y-5'>
            <AxonCard
              className={cn(state.showCpe && `bg-surface-section`, 'border-gradient-border cursor-pointer')}
              onClick={() => isDeviceOnline && setState({ ...state, showCpe: !state.showCpe })}>
              <div style={styleForOverlay} className='flex items-center gap-x-3 p-3 text-xl'>
                {state.showCpe ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                {t('device:cpe.title')}
                {!state.showCpe && (
                  <div className='ml-auto flex items-center gap-x-2'>
                    <AxonBadge variant={'outline'}>{t('device:cpe.cpeStats.title')}</AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('cpeHistory')}>
                      {t('device:cpe.cpeHistory.title')}
                    </AxonBadge>
                  </div>
                )}
              </div>
            </AxonCard>
            {state.showCpe && (
              <>
                <CpeStatistics />
                <CpeHistory />
              </>
            )}

            <AxonCard
              className={cn(state.showLanWLan && `bg-surface-section`, 'border-gradient-border cursor-pointer')}
              id='lanWlan'
              onClick={() => isDeviceOnline && setState({ ...state, showLanWLan: !state.showLanWLan })}>
              <div style={styleForOverlay} className='flex items-center gap-x-3 p-3 text-xl'>
                {state.showLanWLan ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                {t('device:lanWlan.title')}
                {!state.showLanWLan && (
                  <div className='ml-auto flex items-center gap-x-2'>
                    <AxonBadge variant={'outline'}>{t('device:lanWlan.wifiStats.title')}</AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('lanPorts')}>
                      {t('device:lanWlan.lanPorts')}
                    </AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('wifiBands')}>
                      {t('device:lanWlan.wifiBands')}
                    </AxonBadge>
                    <AxonBadge variant={'outline'}>+1</AxonBadge>
                  </div>
                )}
              </div>
            </AxonCard>

            {state.showLanWLan && (
              <>
                <WifiStatistic />
                <LANPorts />
                <WifiBands />
                <LanWlanHistory />
              </>
            )}

            <AxonCard
              className={cn(state.showWanWidgets && `bg-surface-section`, 'border-gradient-border cursor-pointer')}
              id='wan'
              onClick={() => isDeviceOnline && setState({ ...state, showWanWidgets: !state.showWanWidgets })}>
              <div style={styleForOverlay} className='flex items-center gap-x-3 p-3 text-xl'>
                {state.showWanWidgets ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                {t('wan')}
                {!state.showWanWidgets && (
                  <div className='ml-auto flex items-center gap-x-2'>
                    <AxonBadge variant={'outline'}>{t('wan_config')}</AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('wanStatistics')}>
                      {t('wanStatistics')}
                    </AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('wanHistory')}>
                      {t('wanHistory')}
                    </AxonBadge>
                    <AxonBadge variant={'outline'}>+1</AxonBadge>
                  </div>
                )}
              </div>
            </AxonCard>
            {state.showWanWidgets && (
              <>
                <WanConfig />
                <WanStatistics />
                <WanHistory />
                <CellularModem />
              </>
            )}

            <AxonCard
              id='clients'
              className={cn(state.showClientWidgets && `bg-surface-section`, 'border-gradient-border cursor-pointer')}
              onClick={() => isDeviceOnline && setState({ ...state, showClientWidgets: !state.showClientWidgets })}>
              <div style={styleForOverlay} className='flex items-center gap-x-3 p-3 text-xl'>
                {state.showClientWidgets ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                {t('Client')}
                {!state.showClientWidgets && (
                  <div className='ml-auto flex items-center gap-x-2'>
                    <AxonBadge variant={'outline'}>{t('clientsConnectedToThisCPE')}</AxonBadge>
                    <AxonBadge variant={'outline'} onClick={() => handleClickBadge('clientHistory')}>
                      {t('clientHistory')}
                    </AxonBadge>
                  </div>
                )}
              </div>
            </AxonCard>
            {state.showClientWidgets && (
              <>
                <ClientDevice />
                <ClientHistory />
              </>
            )}

            <AxonCard
              className={cn(state.showServicesWidget && `bg-surface-section`, 'border-gradient-border cursor-pointer')}
              id='services'
              onClick={() => isDeviceOnline && setState({ ...state, showServicesWidget: !state.showServicesWidget })}>
              <div style={styleForOverlay} className='flex items-center gap-x-3 p-3 text-xl'>
                {state.showServicesWidget ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                {t('Services')}
                {!state.showServicesWidget && (
                  <div className='ml-auto flex items-center gap-x-2'>
                    <AxonBadge variant={'outline'}>{t('serviceHistory')}</AxonBadge>
                  </div>
                )}
              </div>
            </AxonCard>
            {state.showServicesWidget && (
              <>
                <ServiceHistory />
              </>
            )}
          </div>
        </div>
        <div className='3xl:min-w-[352px] 3xl:flex-col flex w-full flex-1 flex-row gap-6'>
          <div className='insights scrollbar-sm flex-1'>
            <Insights disabled={isOverlayVisible} />
          </div>
          <div className='logs scrollbar-sm flex-1'>
            <Logs disabled={isOverlayVisible} />
          </div>
        </div>
        {isOverlayVisible && (
          <div className='gap-3xl absolute inset-[100px_0_0_0] z-50 flex flex-col items-center' aria-hidden='true'>
            <div className='gap-md flex flex-col items-center'>
              <ZapOff className='text-content-secondary size-12' />
              <p className='text-content-secondary font-global text-2xl font-medium leading-[120%]'>
                {t('deviceIsOffline')}
              </p>
            </div>
            <div>
              <AxonButton variant='primary' onClick={throttle(refetchAll, 3000, { leading: true, trailing: false })}>
                <RefreshCcw className='size-4 text-white' />
                {t('refresh')}
              </AxonButton>
            </div>
          </div>
        )}
      </div>
      {/* TODO show dialog when device is offline */}
      <AxonDialog open={isShowDialogOfflineConfirmation} onOpenChange={setIsShowDialogOfflineConfirmation}>
        <AxonDialogContent
          hideCloseButton
          clickOutsideToClose={false}
          escapeToClose={false}
          className='gap-sm flex w-fit flex-col items-center justify-center text-center'>
          <AxonDialogHeader className='gap-xl items-center'>
            <ZapOff className='text-warning-500 size-10' />
            <AxonDialogTitle className='text-content-primary font-global text-lg font-medium leading-[120%]'>
              {t('deviceIsOffline')}
            </AxonDialogTitle>
          </AxonDialogHeader>
          <AxonDialogDescription
            className='text-content-secondary font-global text-md w-[19rem] leading-[150%]'
            dangerouslySetInnerHTML={{ __html: t('deviceIsOfflineDescription') }}
          />
          <AxonDialogFooter className='mt-xl gap-sm h-8 w-full space-x-0'>
            <AxonButton onClick={closeAndStay} className='basis-1/2'>
              {t('closeAndStay')}
            </AxonButton>
            <AxonButton onClick={goToMostRecent} className='basis-1/2' variant='primary'>
              {t('goToMostRecent')}
            </AxonButton>
          </AxonDialogFooter>
        </AxonDialogContent>
      </AxonDialog>
    </div>
  );
};

export default Device;
