import {
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectItem,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
interface Props {
  wifiBands: Array<{ id: string; title: string }>;
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
}

export const WifiBandSelectDropdown = ({ wifiBands, selectedBandId, setSelectedBandId }: Props) => {
  const { t } = useTranslation();
  useEffect(() => {
    if (!selectedBandId) {
      setSelectedBandId(wifiBands[0].id);
    }
  }, [selectedBandId, setSelectedBandId, wifiBands]);

  return (
    <AxonSelect value={selectedBandId} onValueChange={setSelectedBandId}>
      <AxonSelectTrigger className='w-fit gap-x-2 border-none bg-transparent'>
        <AxonSelectValue placeholder={t('device:lanWLANHistory.selectBand')} />
      </AxonSelectTrigger>
      <AxonSelectContent>
        <AxonSelectGroup>
          <AxonSelectLabel>{t('device:lanWLANHistory.selectBand')}</AxonSelectLabel>
          {wifiBands.map((b) => {
            return (
              <AxonSelectItem key={`${b.title}-${b.id}`} value={b.id}>
                {b.title}
              </AxonSelectItem>
            );
          })}
        </AxonSelectGroup>
      </AxonSelectContent>
    </AxonSelect>
  );
};
