import useTabStore, {
  EUnit,
  useTabLineId,
  useTabDeviceId,
  useTimeRangeSelected,
  useConfigWidgetWanStatistic,
  useConfigDrawer,
} from '@/stores/tab.store';
import { cn } from '@/utils';
import { ColumnDef, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetWanStatisticsDrawer } from 'services/CPEService';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import get from 'lodash/get';
import { CPEDrawers } from '@/constants/cpeElements';
import { Maximize, Search } from 'lucide-react';
import {
  AxonButton,
  AxonDateRangePicker,
  AxonInput,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonTableData,
} from 'ui/UIComponents';

export interface IWanStatistics {
  wanId: string;
  wanType: string;
  wanDown: string | number | null;
  wanDownIssue: string | null;
  wanUp: string | number | null;
  wanUpIssue: string | null;
  wanDescription: string | null;
  wanUnit: string;
}

type IWanDetail = {
  wanId: string;
  wanType: string;
  wanMinValue: string | number | null;
  wanMaxValue: string | number | null;
  wanAverage: string | number | null;
  wanValue: string | number | null;
  noOfTest: number | null;
  wanUnit: string;
};

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const disabledDate = {
  after: new Date(),
};

const WanStatisticsDrawer = () => {
  const { t } = useTranslation();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const setTimeRange = useTabStore((state) => state.setTimeRange);

  // const [searchKey, setSearchKey] = useState('');
  const searchKey = useConfigWidgetWanStatistic()!.searchKey;
  const timeRangeSelected = useTimeRangeSelected();
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);
  const lineId = useTabLineId() || '';
  const deviceId = useTabDeviceId() || '';
  const {
    data: queryWanStatisticDrawerData,
    isLoading,
    isError,
  } = useGetWanStatisticsDrawer({
    customerId: lineId,
    deviceId,
    startDate: getUnixTime(startDate) || 0,
    endDate: getUnixTime(endDate) || undefined,
  });
  const wanStatisticDrawerData = queryWanStatisticDrawerData?.data;
  const { results: detail } = wanStatisticDrawerData ?? {};

  const handleSetCustomTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate,
      endDate,
    });
  };

  const drawer = useConfigDrawer();
  const openDrawer = drawer === CPEDrawers.WAN_STATISTICS;
  const handleDrawerOpenChange = (open: boolean) => {
    addConfigEnhance('drawer', open ? CPEDrawers.WAN_STATISTICS : null);
  };

  const columns = useMemo<ColumnDef<IWanDetail, any>[]>(() => {
    return [
      {
        header: () => <span className='pl-5'>{t('device:wanStatistics.table.header.type')}</span>,
        accessorKey: 'type',
        cell: ({ row }) => {
          const { wanType } = row.original;
          return <span className='text-md text-content-primary pl-5 font-medium'>{wanType}</span>;
        },
      },
      {
        header: t('device:wanStatistics.table.header.minMax'),
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { wanMinValue, wanMaxValue, wanUnit } = row.original;
          return (
            <span className='text-content-primary text-md font-normal'>
              {wanUnit
                ? `${wanMinValue !== null ? wanMinValue : ''} - ${wanMaxValue !== null ? wanMaxValue : ''} ${wanUnit}`
                : ''}
            </span>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.average'),
        accessorKey: 'averageValue',
        cell: ({ row }) => {
          const { wanAverage, wanUnit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {wanAverage !== null ? wanAverage : '-'} {wanUnit}
            </span>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.latestResult'),
        accessorKey: 'value',
        cell: ({ row }) => {
          const { wanValue, wanUnit, wanId } = row.original;
          const isWarning = wanId.includes('speed') || wanId.includes('latency');

          return (
            <div className='flex flex-col items-start'>
              <span className='text-content-primary'>
                {wanValue !== null ? wanValue : '-'} {wanUnit}
              </span>
              {isWarning && (
                <span className='flex items-center gap-1'>
                  {/* <BoldArrowUpSquare color='rgb(var(--content-meta-green))' />{' '}
                  <span className='text-content-meta-green text-xs font-medium'>xx%</span>{' '}
                  <span className='font-book text-xs opacity-60'>vs. SLA (xxMbps)</span> */}
                </span>
              )}
            </div>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.noOfTests'),
        accessorKey: 'noOfTest',
        cell: ({ row }) => {
          const { noOfTest } = row.original;
          return (
            <span className='text-md text-content-primary pr-5 font-normal'>{noOfTest !== null ? noOfTest : ''}</span>
          );
        },
      },
    ];
  }, [t]);

  const rows = useMemo<IWanDetail[]>(() => {
    if (!searchKey || !detail) return detail || [];
    return detail.filter((item) => {
      if (item.wanId.toLowerCase().includes(searchKey.toLowerCase())) return true;
      if (item.wanType.toLowerCase().includes(searchKey.toLowerCase())) return true;
      if ((item.wanUnit ?? '').toLowerCase().includes(searchKey.toLowerCase())) return true;

      return false;
    });
  }, [detail, searchKey]);

  const table = useReactTable({
    columns,
    data: rows,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <AxonSheet open={openDrawer} onOpenChange={handleDrawerOpenChange}>
      <AxonSheetTrigger key='card-cpe-statistics' asChild>
        <AxonButton
          size='icon'
          variant='outline'
          className='bg-surface-action mr-2 border-none p-2 shadow-none'
          aria-label={t('ariaLabel.openWanStats')}>
          <Maximize size={16} />
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent
        className={cn('lg w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-lg 2xl:max-w-screen-xl')}>
        <div className='flex h-screen flex-col'>
          <div className={cn('h-16 border-b px-6 py-5 text-xl font-medium')}>{t('wanStatistics')}</div>
          <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
            <div className='relative w-96'>
              <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
              <AxonInput
                placeholder={t('device:wanStatistics.searchInputPlaceholder')}
                className='pl-10'
                value={searchKey}
                onChange={(e) => addConfigEnhance('widgets.wanStatistic.searchKey', e.target.value)}
              />
            </div>
            <div>
              <AxonDateRangePicker
                selected={{
                  from: startDate ?? undefined,
                  to: endDate ?? undefined,
                }}
                disabled={disabledDate}
                onApply={handleSetCustomTimeRange}
                showIcon={false}
              />
            </div>
          </div>

          <div className={cn('bg-surface-section scrollbar-lg h-full overflow-auto')}>
            <AxonTableData
              table={table}
              showFooter={false}
              headerRowClassName='bg-surface-section'
              bodyRowClassName='bg-surface-section'
              isLoading={isLoading}
              isError={isError}
            />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default WanStatisticsDrawer;
