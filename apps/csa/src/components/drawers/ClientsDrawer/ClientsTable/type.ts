import { EHealthCheckStatus } from '@/components/HealthCheck/enum';

interface DeviceInfo {
  deviceName: string;
  isOnline: boolean;
  lastSeenTimestamp: number; // UNIX timestamp
  deviceType: string; // e.g. 'iphone', 'tv', 'laptop'
}

interface NetworkConnection {
  connectionType: 'wifi' | 'ethernet';
  band: string;
  description: string; // e.g., SSID or Ethernet port name
  connectionInterface: string | null;
}

interface NetworkAddress {
  ipAddress: string;
  macAddress: string;
}

interface DataUsageInfo {
  totalUsedGB: number; // e.g., 1.5
  percentChangeFromLastWeek: number; // e.g., 31
}

interface InsightStats {
  eventsCount: number;
  lastUpdatedTimestamp: number; // UNIX timestamp
}

interface SignalStrength {
  rssiValue: number; // e.g., -40 (strong) to -90 (weak)
  coverageDetection: number;
  status: EHealthCheckStatus | null;
}

export interface DeviceRecord {
  cpeId: string;
  deviceInfo: DeviceInfo;
  networkConnection: NetworkConnection;
  networkAddress: NetworkAddress;
  dataUsage: DataUsageInfo;
  insights: InsightStats;
  signalStrength: SignalStrength;
  parentalControls: {
    enabled: boolean;
    stationMac: string;
    indefinite: boolean;
    parentalRestrictionId: string;
  }[];
}
