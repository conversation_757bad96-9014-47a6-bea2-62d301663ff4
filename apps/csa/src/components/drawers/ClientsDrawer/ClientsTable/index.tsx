import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search, X } from 'ui/UIAssets';
import {
  AxonButton,
  AxonInput,
  AxonSelectWrapper,
  AxonSheetClose,
  AxonTableData,
  AxonToggleGroup,
  AxonToggleGroupItem,
} from 'ui/UIComponents';
import { useClientTableAction } from './ClientTable.action';
import { useColumn } from './columns';
import { useTranslation } from 'react-i18next';

export type ClientsTableProps = {
  onButtonClick?: () => void;
};

const ClientsTable = (props: ClientsTableProps) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { onButtonClick } = props;
  const { t } = useTranslation();

  const columns = useColumn();
  const { selectionOptions, filteredData, filterState, setFilterState, debouncedSearchInput, isLoading } =
    useClientTableAction();

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className='flex h-full flex-col gap-3'>
      <div className='bg-surface-body h-d16 border-border-primary flex shrink-0 items-center justify-between border-b px-6'>
        <div>
          <p className='text-content-primary text-xl font-medium'>
            {t('customer:healthCheck.wheel.client.drawer.summary.clients')}{' '}
            <span className='text-content-tertiary'>{filteredData.length}</span>
          </p>
        </div>
        <div>
          {/* <AxonButton variant={'ghost'} className='ml-auto' onClick={onButtonClick}>
            <Repeat className='text-component-hyperlink mr-2 size-4' />{' '}
            <p className='text-component-hyperlink font-book text-xs'>
              {t('customer:healthCheck.wheel.client.drawer.summary.switchToStats')}
            </p>
          </AxonButton>
          */}
          <AxonSheetClose>
            <AxonButton variant={'outline'} size={'icon'}>
              <X className='size-4' />
            </AxonButton>
          </AxonSheetClose>
        </div>
      </div>
      <div className='bg-surface-body border-border-primary flex h-[64px] items-center gap-x-3 px-6'>
        <AxonInput
          placeholder={t('customer:healthCheck.wheel.client.drawer.summary.searchByDeviceName')}
          startDecorator={<Search />}
          className='w-80'
          defaultValue={filterState.search}
          onChange={(e) => debouncedSearchInput(e.target.value)}
        />
        <div className='ml-auto'>
          <AxonSelectWrapper
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.cpe')}
            value={filterState.cpeId}
            onChange={(value) => setFilterState('cpeId', value)}
            options={selectionOptions.cpeIdsOptions as string[]}
          />
        </div>
        <div>
          <AxonSelectWrapper
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.type')}
            value={filterState.deviceType}
            onChange={(value) => setFilterState('deviceType', value)}
            options={selectionOptions.deviceTypesOptions as string[]}
          />
        </div>
        <div>
          <AxonSelectWrapper
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.connection')}
            value={filterState.connectionInterface}
            onChange={(value) => setFilterState('connectionInterface', value)}
            options={selectionOptions.connectionInterfacesOptions as string[]}
          />
        </div>
        <div>
          <AxonSelectWrapper
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.parental')}
            value={filterState.parentalControl}
            onChange={(value) => setFilterState('parentalControl', value)}
            options={selectionOptions.parentalControlOptions}
          />
        </div>
        <AxonToggleGroup
          onValueChange={(value) => setFilterState('status', value as 'online' | 'offline')}
          value={filterState.status}
          type='single'
          className='h-[40px]'
          defaultValue='all'>
          <AxonToggleGroupItem value='all'>
            {t('customer:healthCheck.wheel.client.drawer.summary.all')}
          </AxonToggleGroupItem>
          <AxonToggleGroupItem value='online'>
            {t('customer:healthCheck.wheel.client.drawer.summary.connected')}
          </AxonToggleGroupItem>
          <AxonToggleGroupItem value='offline'>
            {t('customer:healthCheck.wheel.client.drawer.summary.disconnected')}
          </AxonToggleGroupItem>
        </AxonToggleGroup>
      </div>
      <div className='bg-surface-section border-border-primary flex-1 overflow-auto border-t'>
        <AxonTableData
          classes={{ wrapper: 'max-h-screen' }}
          table={table}
          showFooter={false}
          showSelect={filteredData.length > 0 ? 'first' : undefined}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default ClientsTable;
