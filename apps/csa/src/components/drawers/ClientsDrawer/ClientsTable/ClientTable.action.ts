import useTabStore, {
  useConfigWidgetClientConnection,
  useTabDeviceId,
  useTabLineId,
  useTabType,
  useTimeRangeSelected,
} from '@/stores/tab.store';
import debounce from 'lodash/debounce';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetClientConnection } from 'services/Client';
import { getUnixTime } from 'services/Utils';
import get from 'lodash/get';
import { SortingState } from '@tanstack/react-table';

export const ALL_VALUE_OPTION = 'all';

export const useClientTableAction = () => {
  const { t } = useTranslation();
  const tabType = useTabType();
  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const isDeviceTab = tabType === 'device';
  const timeRangeSelected = useTimeRangeSelected();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const clientConnectionConfig = useConfigWidgetClientConnection();
  const filterState = get(clientConnectionConfig, 'filterState')!;
  const sorting = get(clientConnectionConfig, 'sorting')! as SortingState;

  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const {
    data: healthCheckClientExtended,
    isError: isHealthCheckClientError,
    ...rest
  } = useGetClientConnection(
    {
      customerId: lineId ?? '',
      deviceId: isDeviceTab ? (deviceId ?? '') : '',
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: isDeviceTab ? !!deviceId : !!lineId,
      staleTime: 3 * 60 * 1000,
    },
  );

  const handleChangeSorting = useCallback(
    (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
      const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
      addConfigEnhance('widgets.clientConnection.sorting', newSorting);
    },
    [sorting, addConfigEnhance],
  );

  const debouncedSearchInput = useMemo(() => {
    return debounce((value: string) => {
      addConfigEnhance('widgets.clientConnection.filterState.search', value);
    }, 500);
  }, [addConfigEnhance]);

  const handleChangeFilterState = useCallback(
    (key: string, value: any) => {
      const path = `widgets.clientConnection.filterState.${key}`;
      addConfigEnhance(path, value);
    },
    [addConfigEnhance],
  );

  const filteredData = useMemo(() => {
    const { results } = healthCheckClientExtended?.data || {};

    if (!results) {
      return [];
    }

    return results.filter((item) => {
      const { cpeId, deviceType, connectionInterface, parentalControl, status, search } = filterState;
      const isFilterCpeId = cpeId === '' || cpeId === ALL_VALUE_OPTION || item.cpeId === cpeId;
      const isFilterDeviceType =
        deviceType === '' || deviceType === ALL_VALUE_OPTION || item.deviceInfo.deviceType === deviceType;
      const isFilterConnectionInterface =
        connectionInterface === '' ||
        connectionInterface === ALL_VALUE_OPTION ||
        item.networkConnection.connectionInterface === connectionInterface;

      let isFilterParentalControl = true;
      if (parentalControl === 'enabled') {
        // When an item has enabled parental control, parentalControls has two items, one has 'enabled' field as true and one is false
        isFilterParentalControl = item.parentalControls.some((control) => control.enabled === true);
      } else if (parentalControl === 'disabled') {
        isFilterParentalControl = item.parentalControls.every((control) => control.enabled === false);
      }

      const isFilterStatus =
        status === '' || status === ALL_VALUE_OPTION || status === (item.deviceInfo.isOnline ? 'online' : 'offline');

      const isFilterSearch = search === '' || item.deviceInfo.deviceName.toLowerCase().includes(search.toLowerCase());
      return (
        isFilterCpeId &&
        isFilterDeviceType &&
        isFilterConnectionInterface &&
        isFilterParentalControl &&
        isFilterStatus &&
        isFilterSearch
      );
    });
  }, [healthCheckClientExtended, filterState]);

  const selectionOptions = useMemo(() => {
    const { cpeIds, deviceTypes, connectionInterfaces } = healthCheckClientExtended?.data || {};
    return {
      cpeIdsOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allCpe'),
          value: ALL_VALUE_OPTION,
        },
        ...(cpeIds || []),
      ],
      deviceTypesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allType'),
          value: ALL_VALUE_OPTION,
        },
        ...(deviceTypes || []),
      ],
      connectionInterfacesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allConnection'),
          value: ALL_VALUE_OPTION,
        },
        ...(connectionInterfaces || []),
      ],
      parentalControlOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.parental'),
          value: ALL_VALUE_OPTION,
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.enable'),
          value: 'enabled',
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.disable'),
          value: 'disabled',
        },
      ],
    };
  }, [healthCheckClientExtended, t]);

  return {
    ...rest,
    isError: isHealthCheckClientError,
    data: healthCheckClientExtended,
    filteredData,
    filterState,
    setFilterState: handleChangeFilterState,
    sorting,
    handleChangeSorting,
    selectionOptions,
    debouncedSearchInput,
  };
};
