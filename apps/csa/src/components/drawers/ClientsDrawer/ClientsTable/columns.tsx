import { EHealthCheckStatus } from '@/components/HealthCheck/enum';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { getRelativeTimeFromNow } from 'services/Utils';
import {
  BoldSquareArrowDown,
  BoldSquareArrowUp,
  getDeviceImage,
  GitMerge,
  MoreHorizontal,
  Signifier,
  WiFi,
} from 'ui/UIAssets';
import { AxonButton, AxonImage, ProgressBar } from 'ui/UIComponents';
import { DeviceRecord } from './type';
import { useTranslation } from 'react-i18next';

export const useColumn = () => {
  const { t } = useTranslation();
  const columns: ColumnDef<DeviceRecord>[] = useMemo(
    () => [
      {
        accessorKey: 'cpeId',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.cpeId'),
      },
      {
        accessorKey: 'deviceInfo',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.device'),
        cell: ({ getValue }) => {
          const device = getValue<DeviceRecord['deviceInfo']>();
          const relativeTime = getRelativeTimeFromNow(device.lastSeenTimestamp);
          const color = device.isOnline ? 'text-content-meta-green' : 'text-content-tertiary';

          return (
            <div className='flex items-center gap-x-2'>
              <AxonImage
                src={getDeviceImage(device.deviceType)}
                alt={t('device:deviceImageAlt')}
                size={'xs'}
                className='p-1'
              />
              <div className='flex flex-col'>
                <span className='font-medium'>{device.deviceName}</span>
                <div className='flex items-center gap-x-1'>
                  <Signifier className={`${color} size-2`} />
                  <p className='text-content-tertiary font-book text-xs'>
                    {device.isOnline
                      ? t('customer:healthCheck.wheel.client.drawer.summary.body.online')
                      : t('customer:healthCheck.wheel.client.drawer.summary.body.offline')}{' '}
                    {relativeTime ? `, ${relativeTime}` : ''}
                  </p>
                </div>
              </div>
            </div>
          );
        },
        sortingFn: (rowA, rowB, columnId) => {
          const a = rowA.getValue<DeviceRecord['deviceInfo']>(columnId);
          const b = rowB.getValue<DeviceRecord['deviceInfo']>(columnId);
          return a.deviceName.localeCompare(b.deviceName);
        },
      },
      {
        accessorKey: 'networkConnection',
        header: t('customer:healthCheck.wheel.client.drawer.summary.connection'),
        cell: ({ getValue }) => {
          const conn = getValue<DeviceRecord['networkConnection']>();
          const icon = conn.connectionType === 'wifi' ? <WiFi className='size-4' /> : <GitMerge className='size-4' />;
          return (
            <div className='flex items-center gap-x-2'>
              <div className='flex flex-col'>
                <div className='flex items-center gap-x-2 capitalize'>
                  {icon}
                  {conn.connectionInterface}
                </div>
                <div className='text-content-tertiary flex items-center gap-x-2 text-xs'>
                  <div className='w-4' />
                  {conn.description}
                </div>
              </div>
            </div>
          );
        },
        sortingFn: (rowA, rowB, columnId) => {
          const a = rowA.getValue<DeviceRecord['networkConnection']>(columnId);
          const b = rowB.getValue<DeviceRecord['networkConnection']>(columnId);
          return a.connectionType.localeCompare(b.connectionType);
        },
      },
      {
        accessorKey: 'networkAddress',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.ipMac'),
        cell: ({ getValue }) => {
          const net = getValue<DeviceRecord['networkAddress']>();
          return (
            <div>
              <div>{net.ipAddress}</div>
              <div className='text-content-tertiary text-xs'>{net.macAddress}</div>
            </div>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'dataUsage',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.dataUsage'),
        cell: ({ getValue }) => {
          const data = getValue<DeviceRecord['dataUsage']>();
          const { percentChangeFromLastWeek } = data;
          const isPositive = percentChangeFromLastWeek !== null && percentChangeFromLastWeek > 0;
          return (
            <div>
              {data.totalUsedGB !== null && <div>{data.totalUsedGB} MB</div>}
              {percentChangeFromLastWeek !== null && (
                <div className='flex items-center gap-x-1'>
                  {isPositive ? (
                    <BoldSquareArrowUp className='text-content-meta-green size-4' />
                  ) : (
                    <BoldSquareArrowDown className='text-content-meta-red size-4' />
                  )}
                  <div
                    style={{
                      color: isPositive ? 'rgb(var(--content-meta-green))' : 'rgb(var(--content-meta-red))',
                    }}
                    className='text-xs'>
                    {percentChangeFromLastWeek}% {t('customer:healthCheck.wheel.client.drawer.summary.body.vsLastWeek')}
                  </div>
                </div>
              )}
            </div>
          );
        },
        sortingFn: (rowA, rowB, columnId) => {
          const a = rowA.getValue<DeviceRecord['dataUsage']>(columnId);
          const b = rowB.getValue<DeviceRecord['dataUsage']>(columnId);
          return a.totalUsedGB - b.totalUsedGB;
        },
      },
      {
        accessorKey: 'insights',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.insights'),
        cell: ({ getValue }) => {
          const insights = getValue<DeviceRecord['insights']>();
          return (
            insights.lastUpdatedTimestamp !== null && (
              <div>
                <div>{insights.eventsCount}</div>
                <div className='text-content-tertiary text-xs'>
                  {t('customer:healthCheck.wheel.client.drawer.summary.body.latest')}{' '}
                  {getRelativeTimeFromNow(insights.lastUpdatedTimestamp)}
                </div>
              </div>
            )
          );
        },
        sortingFn: (rowA, rowB, columnId) => {
          const a = rowA.getValue<DeviceRecord['insights']>(columnId);
          const b = rowB.getValue<DeviceRecord['insights']>(columnId);
          return a.eventsCount - b.eventsCount;
        },
      },
      {
        accessorKey: 'signalStrength',
        header: t('customer:healthCheck.wheel.client.drawer.summary.header.rssi'),
        cell: ({ getValue, row }) => {
          const { rssiValue, status } = getValue<DeviceRecord['signalStrength']>();
          const rssiPercentage = 100 + rssiValue;
          const color =
            status === EHealthCheckStatus.VERY_UNSTABLE
              ? 'rgb(var(--error-500))'
              : status === EHealthCheckStatus.UNSTABLE
                ? 'rgb(var(--warning-500))'
                : status === EHealthCheckStatus.STABLE
                  ? 'rgb(var(--success-500))'
                  : 'rgb(var(--content-tertiary))';

          const hideRssi = row.original.networkConnection.connectionType === 'ethernet';
          if (hideRssi) return null;

          return (
            <div className='w-20'>
              <ProgressBar
                total={100}
                values={[{ value: rssiPercentage, label: 'RSSI', color: color }]}
                tootip={
                  <div>
                    {t('customer:healthCheck.wheel.client.drawer.summary.body.rssiValue')}: {rssiValue}
                  </div>
                }
              />
            </div>
          );
        },
      },
      {
        id: 'actions',
        cell: () => {
          return (
            <AxonButton disabled aria-label={t('ariaLabel.moreActions')}>
              <MoreHorizontal />
            </AxonButton>
          );
        },
      },
    ],
    [t],
  );

  return columns;
};
