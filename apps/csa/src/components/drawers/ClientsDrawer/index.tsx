import { CPEDrawers } from '@/constants/cpeElements';
import useTabStore, { useConfigDrawer } from '@/stores/tab.store';
import { Maximize } from 'lucide-react';
import { useState } from 'react';
import { AxonButton, AxonSheet, AxonSheetContent, AxonSheetTrigger } from 'ui/UIComponents';
import ClientsStats from './ClientsStats';
import ClientsTable from './ClientsTable';
import { useTranslation } from 'react-i18next';

interface ClientsDrawerProps {
  disabled?: boolean;
}

const ClientsDrawer = ({ disabled = false }: ClientsDrawerProps) => {
  const [isSummaryView, setIsSummaryView] = useState(true);
  const onButtonClick = () => setIsSummaryView((prev) => !prev);
  const { t } = useTranslation();

  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const drawer = useConfigDrawer();

  const openDrawer = drawer === CPEDrawers.CLIENTS;
  const handleDrawerOpenChange = (open: boolean) => {
    addConfigEnhance('drawer', open ? CPEDrawers.CLIENTS : null);
  };

  return (
    <AxonSheet open={openDrawer} onOpenChange={handleDrawerOpenChange}>
      <AxonSheetTrigger disabled={disabled} asChild>
        <AxonButton className='p-2' variant='ghost' size='icon' aria-label={t('ariaLabel.openClients')}>
          <Maximize size={1.5} className='text-content-primary size-4' />
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='w-4/5 max-w-none p-0' hideCloseButton>
        {isSummaryView ? (
          <ClientsTable onButtonClick={onButtonClick} />
        ) : (
          <ClientsStats onButtonClick={onButtonClick} />
        )}
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default ClientsDrawer;
