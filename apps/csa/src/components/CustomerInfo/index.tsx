import { useTranslation } from 'react-i18next';

import {
  AxonButton,
  AxonCard,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonSeparator,
} from 'ui/UIComponents';

import { useGetLineInfo } from 'services/LineInfo';
import { useTabLineId } from '@/stores/tab.store';
import { BoldCard, BoldHome, Book, MoreHorizontal } from 'ui/UIAssets';
import CustomerBadges from './CustomerBadges';
import CustomerDetails from './CustomerDetails';
import { CustomerInfoSkeleton } from './CustomerInfoSkeleton';
import CustomerViewNumber from './CustomerViewNumber';
import ISPInfo from './ISPInfo';
import LogDrawer from '@/components/Logs/LogDrawer';
import { useState } from 'react';
import CustomerNoData from './CustomerNoData';
import CustomerError from './CustomerError';

const CustomerInfo = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId();
  const { data, isLoading, isError } = useGetLineInfo(lineId ?? '');
  const [openDrawer, setOpenDrawer] = useState(false);

  if (isLoading) return <CustomerInfoSkeleton />;
  if (isError) return <CustomerError />;
  if (!data?.data) return <CustomerNoData />;

  const lineInfo = data.data.network ?? {};
  return (
    <AxonCard>
      <div className='flex h-[135px] flex-row items-center gap-x-5 p-5 font-medium'>
        <div className='border-gradient-border rounded-md border p-2'>
          <BoldHome className='text-content-tertiary size-16' />
        </div>

        <div className='scrollbar-md flex flex-col gap-y-1 overflow-auto'>
          <p className='text-content-tertiary text-xs uppercase leading-tight'>
            {t('customer:customerInfo.customerNetwork')}
          </p>
          <div className='mb-2 flex items-center gap-2'>
            <p data-testid={`customer-info-network-id`} className='text-2xl leading-tight'>
              {lineInfo?.lineId}
            </p>
            <CustomerBadges />
          </div>
          <div className='flex items-center gap-3'>
            <CustomerDetails />
            <ISPInfo />
            {(lineInfo.servicePlanName || lineInfo.phoneNumber) && (
              <AxonSeparator orientation='vertical' className='h-3' />
            )}
            <div className='flex items-center gap-1 *:text-sm'>
              {lineInfo.servicePlanName && (
                <p
                  data-testid={`customer-info-service-plan`}
                  className='text-component-hyperlink flex items-center gap-1'>
                  <BoldCard className='size-4' aria-hidden={true} />
                  {lineInfo.servicePlanName}
                </p>
              )}
              {lineInfo.phoneNumber && <CustomerViewNumber phoneNumber={lineInfo.phoneNumber} />}
            </div>
          </div>
        </div>
        <div className='ml-auto'>
          <AxonDropdownMenu>
            <AxonDropdownMenuTrigger asChild>
              <AxonButton
                aria-label={t('ariaLabel.moreActions')}
                data-testid={`customer-info-more-button`}
                variant={'outline'}
                size='icon'>
                <MoreHorizontal />
              </AxonButton>
            </AxonDropdownMenuTrigger>
            <AxonDropdownMenuContent>
              <AxonDropdownMenuItem
                data-testid={`customer-info-more-button-log`}
                className='cursor-pointer'
                onClick={() => setOpenDrawer(true)}>
                <Book className='mr-2 size-4' />
                {t('logList')}
              </AxonDropdownMenuItem>
            </AxonDropdownMenuContent>
          </AxonDropdownMenu>
        </div>
      </div>
      <LogDrawer open={openDrawer} onOpenChange={setOpenDrawer} />
    </AxonCard>
  );
};

export default CustomerInfo;
