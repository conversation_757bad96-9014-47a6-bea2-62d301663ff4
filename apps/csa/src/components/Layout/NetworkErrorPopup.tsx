import { useGetUserProfile } from 'services/User';
import { AxonAlertDialogWrapper } from 'ui/UIComponents';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const NetworkErrorPopup = () => {
  const { error } = useGetUserProfile();
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation();
  const errorMessage = useMemo(() => {
    if (error) {
      const message = error['response']['data']['message'];
      const code = error['status'];

      if (typeof code === 'number' && code >= 400) {
        return typeof message === 'string' ? message : '';
      }
    }

    return '';
  }, [error]);

  useEffect(() => {
    if (errorMessage) {
      setIsOpen(true);
    }
  }, [errorMessage]);

  return (
    <AxonAlertDialogWrapper
      open={isOpen && !!errorMessage}
      onOpenChange={setIsOpen}
      title={t('networkError')}
      description={errorMessage}
      cancelText={t('ok')}
      confirmText=''
      variant='danger'
    />
  );
};
