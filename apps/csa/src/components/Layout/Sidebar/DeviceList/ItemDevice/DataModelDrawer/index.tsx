import useTabStore, { useTabLineId } from '@/stores/tab.store';
import { cn } from '@/utils';
import {
  ColumnDef,
  ExpandedState,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronRight, Ellipsis, Search, Wifi, Zap, ZapOff, Globe, GitMerge } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDataModel } from 'services/Customer';
import { getDeviceImage, Server2 } from 'ui/UIAssets';
import {
  AxonButton,
  AxonImage,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonTableData,
  AxonTableInputSearch,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonTooltipWrapper,
  AxonTooltip,
  AxonTooltipTrigger,
  AxonTooltipContent,
  AxonTooltipProvider,
  AxonTooltipPortal,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { getRelativeTimeFromNow } from 'services/Utils';
import { default as EventIcon } from '@/assets/info.svg';
import { default as LogIcon } from '@/assets/code.svg';
import { convertQOEStatus, QOEStatusEnum } from '@/utils/QOE.util';
import { EHealthCheckStatus } from '@/components/HealthCheck/enum';

const DataModelDrawer = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId();

  const { setActiveTab, addTab } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
    })),
  );

  const { data: dataModel, isLoading } = useGetDataModel(lineId || '', { enabled: !!lineId });
  const data = useMemo(() => dataModel?.data?.result || [], [dataModel]);

  const [globalFilter, setGlobalFilter] = useState('');
  const [expanded, setExpanded] = useState<ExpandedState>(true);
  const [open, setOpen] = useState(false);

  const goToCpePage = useCallback(
    (deviceId: string) => {
      if (!lineId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: lineId,
      });
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
    },
    [lineId, addTab, setActiveTab],
  );

  const columns = useMemo<ColumnDef<(typeof data)[number], any>[]>(
    () => [
      {
        accessorFn: (row) => row.device.name,
        header: t('deviceId'),
        cell: ({ row }) => {
          const { deviceType, name, statuses } = row.original.device;
          const isRoot = name === 'WAN';
          const canExpand = row.getCanExpand();

          const qoeStatus = convertQOEStatus(statuses.qoe.status);
          const lastTestedQOE = statuses.qoe.lastTested;

          return (
            <div
              style={{
                paddingLeft: `${(row.depth - 1) * 3}rem`,
              }}>
              <div className={cn('flex items-center gap-x-2')}>
                {canExpand && !isRoot ? (
                  <AxonButton onClick={row.getToggleExpandedHandler()} variant='ghost' size='default'>
                    {row.getIsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                  </AxonButton>
                ) : null}
                {!canExpand && !isRoot && <div className='w-10'></div>}
                <AxonImage
                  src={getDeviceImage(deviceType || '')}
                  size={'xs'}
                  className='p-1'
                  label={deviceType && ['Router', 'Extender'].includes(deviceType) ? 'axon' : undefined}
                  alt='Data Model Image'
                />
                <div className='flex flex-col'>
                  <p className='text-content-primary font-medium'>{name}</p>

                  <span className='flex items-center gap-1 text-xs'>
                    <div
                      className={cn('bg-content-disabled size-2 rounded-full', {
                        'bg-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
                        'bg-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
                        'bg-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
                        'bg-content-tertiary': !statuses.online.isAlive,
                      })}
                    />
                    <span
                      className={cn(`text-content-disabled font-medium`, {
                        'text-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
                        'text-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
                        'text-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
                        'text-content-tertiary': !statuses.online.isAlive,
                      })}>
                      {t(qoeStatus)}
                    </span>
                    {lastTestedQOE && (
                      <span className='font-book text-content-primary opacity-60'>
                        {`${t('since')} ${getRelativeTimeFromNow(lastTestedQOE)}`}
                      </span>
                    )}
                  </span>

                  <div className='text-content-tertiary flex flex-row items-center gap-1 text-xs'>
                    {statuses.online.isAlive ? (
                      <>
                        <Zap size={12} className='text-content-tertiary' />
                        <p>{t('online')}</p>
                      </>
                    ) : (
                      <>
                        <ZapOff size={12} className='text-content-tertiary' />
                        <p>{t('offline')}</p>
                      </>
                    )}
                    {statuses.online.lastTested && (
                      <p>{`${t('since')} ${getRelativeTimeFromNow(statuses.online.lastTested)}`}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorFn: (row) => row.deviceInfo.modelName,
        header: t('deviceInfo'),
        cell: ({ row }) => {
          const { modelName, osVersion } = row.original.deviceInfo;
          return (
            <div className='flex flex-col gap-1'>
              <p className='text-md text-content-primary'>{modelName}</p>
              <p className='text-content-tertiary text-xs'>{osVersion}</p>
            </div>
          );
        },
      },
      {
        id: 'connection',
        header: t('connection'),
        cell: ({ row }) => {
          const {
            connectedInterface,
            connectedBandName,
            connectedMode,
            connectionType,
            coverageDetectionStatus,
            rssi,
          } = row.original.connection;
          const parentDeviceName = row.original.parentDeviceName;
          const isRootAP = connectedInterface === 'WAN';
          const isEthernet = connectionType === 'ethernet';
          const isWifi = connectionType === 'wifi';
          const color =
            coverageDetectionStatus === EHealthCheckStatus.VERY_UNSTABLE
              ? 'rgb(var(--error-500))'
              : coverageDetectionStatus === EHealthCheckStatus.UNSTABLE
                ? 'rgb(var(--warning-500))'
                : coverageDetectionStatus === EHealthCheckStatus.STABLE
                  ? 'rgb(var(--success-500))'
                  : 'rgb(var(--content-tertiary))';
          return (
            <div className='flex flex-col gap-1'>
              <div className='flex flex-row items-center gap-2'>
                {isEthernet && <GitMerge size={16} />}
                {isWifi && <Wifi size={16} color={color} />}
                {isRootAP && <Globe size={16} />}
                {parentDeviceName && parentDeviceName !== 'WAN' && !isEthernet ? (
                  <span className='text-md text-content-primary flex items-center gap-1 font-medium'>
                    <AxonTooltipWrapper
                      label={
                        <p className='max-w-40 overflow-hidden text-ellipsis text-nowrap'>
                          {`${rssi ? `${rssi} dBm,` : ''} ${connectedInterface ? connectedInterface : t('unknown')}`}
                          <span className='text-sm tracking-wider'>{connectedMode ? ` (${connectedMode})` : ''}</span>
                        </p>
                      }
                      content={
                        <p>
                          {connectedInterface}
                          <span className='text-sm tracking-wider'>{connectedMode ? ` (${connectedMode})` : ''}</span>
                        </p>
                      }
                    />
                  </span>
                ) : (
                  <p className='text-md text-content-primary font-medium'>{connectedInterface}</p>
                )}
              </div>
              <p className='text-content-tertiary pl-6 text-xs'>
                {isEthernet && !connectedBandName ? t('notDetermined') : connectedBandName}
              </p>
            </div>
          );
        },
      },
      {
        id: 'ipMac',
        accessorFn: (row) => `${row.ipMac.ipAddress ?? ''} ${row.ipMac.stationMac ?? ''}`,
        header: t('IP/MAC'),
        cell: ({ row }) => {
          const { ipAddress, stationMac } = row.original.ipMac;
          return (
            <div className='flex flex-col gap-1'>
              <p className='text-md text-content-primary'>{ipAddress}</p>
              <p className='text-content-tertiary text-xs'>{stationMac}</p>
            </div>
          );
        },
      },
      {
        id: 'speed',
        header: t('speed'),
        cell: ({ row }) => {
          const { upload, download, unit, latency, lastTested } = row.original.speed;

          return (
            <div className='flex flex-col gap-1'>
              <div className='text-md text-content-primary flex flex-row gap-1 whitespace-nowrap'>
                <div>{upload}</div>
                <div>{upload && download ? `/ ${download}` : download}</div>
                {(upload || download) && <div>{unit}</div>}
                {latency ? <div>({latency})</div> : null}
              </div>
              {lastTested ? (
                <p className='text-content-tertiary text-xs'>{`${t('since')} ${getRelativeTimeFromNow(1750244948000)}`}</p>
              ) : null}
            </div>
          );
        },
      },
      {
        id: 'counts',
        header: () => (
          <AxonTooltipProvider delayDuration={100}>
            <AxonTooltip>
              <AxonTooltipTrigger>{t('sevenDaysHistory')}</AxonTooltipTrigger>
              {/*// https://github.com/shadcn-ui/ui/issues/129*/}
              <AxonTooltipPortal>
                <AxonTooltipContent side='top'>{t('diagnoseLastWeek')}</AxonTooltipContent>
              </AxonTooltipPortal>
            </AxonTooltip>
          </AxonTooltipProvider>
        ),
        cell: ({ row }) => {
          const { insights, logs } = row.original.counts;
          return (
            <div className='flex flex-row gap-2'>
              {!insights && !logs ? (
                <p className='text-md text-content-primary'>{t('insights.noInsight')}</p>
              ) : (
                <div className='flex flex-row gap-3'>
                  <div className='flex flex-row items-center justify-center gap-1'>
                    <EventIcon />
                    <div>{insights ?? 0}</div>
                  </div>
                  <div className='flex flex-row items-center justify-center gap-1'>
                    <LogIcon />
                    <div>{logs ?? 0}</div>
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        id: 'more',
        header: '',
        cell: ({ row }) =>
          row.original.device.deviceType && ['Router', 'Extender'].includes(row.original.device.deviceType) ? (
            <AxonDropdownMenu modal={false}>
              <AxonDropdownMenuTrigger asChild>
                <AxonButton variant='outline' size='icon'>
                  <Ellipsis size={16} />
                </AxonButton>
              </AxonDropdownMenuTrigger>
              <AxonDropdownMenuContent>
                <AxonDropdownMenuItem
                  onClick={() => {
                    goToCpePage(row.original.device.name);
                    setOpen(false);
                  }}>
                  {t('devicesDrawer.go_to_device_page')}
                </AxonDropdownMenuItem>
                {/*<AxonDropdownMenuItem>{t('devicesDrawer.go_to_rssi_history')}</AxonDropdownMenuItem>*/}
                {/*<AxonDropdownMenuItem>{t('devicesDrawer.go_to_event_timeline')}</AxonDropdownMenuItem>*/}
                {/*<AxonDropdownMenuItem>{t('devicesDrawer.open_insights_panel')}</AxonDropdownMenuItem>*/}
              </AxonDropdownMenuContent>
            </AxonDropdownMenu>
          ) : null,
      },
    ],
    [goToCpePage, t],
  );

  const devices = useMemo(() => data.filter((device) => !device.parentDeviceName), [data]);

  const table = useReactTable({
    data: devices,
    columns,
    state: {
      globalFilter,
      expanded: expanded,
    },
    onExpandedChange: setExpanded,
    getSubRows: (row) => data.filter((device) => device.parentDeviceName === row.device.name),
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    filterFromLeafRows: true,
    getColumnCanGlobalFilter: () => true,
  });

  const handleFilterChange = useCallback((value: string) => {
    setGlobalFilter(String(value));
  }, []);

  return (
    <AxonSheet open={open} onOpenChange={setOpen}>
      <AxonSheetTrigger asChild>
        <AxonButton
          variant='pure'
          aria-label={t('ariaLabel.openWorkbench')}
          data-testid={`sidebar-device-workbench-button`}
          className='flex items-center gap-1 px-0 py-2'>
          <Server2 className='text-component-hyperlink size-5' />
          <p className='text-component-hyperlink text-xs font-medium'>{t('workbench')}</p>
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='max-w-[80%] p-0' onClick={(e) => e.stopPropagation()}>
        <div className='flex h-full flex-col'>
          <p className='border-b-border-flat h-16 border-b px-6 py-5 text-xl font-medium'>
            {t('workbench')}
            <span className='text-content-tertiary ml-2 font-normal'>
              {Math.max(data.length - 1, 0)} {t('devices')}
            </span>
          </p>
          <div className='border-b-border-flat insight-search-block flex items-center justify-between border-b px-6 py-4'>
            <div className='relative h-fit'>
              <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />
              <AxonTableInputSearch
                value={globalFilter}
                onChange={handleFilterChange}
                placeholder={t('searchByDeviceInfo')}
                className='w-80'
              />
            </div>
          </div>
          <AxonTableData
            table={table}
            showFooter={false}
            isLoading={isLoading}
            classes={{
              wrapper: 'bg-surface-section text-content-primary/75',
            }}
          />
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default DataModelDrawer;
