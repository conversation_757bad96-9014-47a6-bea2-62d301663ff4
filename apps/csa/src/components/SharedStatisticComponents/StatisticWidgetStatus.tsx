import { convertQOEStatus, QOEStatusEnum } from '@/utils/QOE.util';
import { CpeStatusIcon } from '@/pages/Device/CpeStatistics/icons/CpeStatusIcon';
import { cn } from '@/utils';
import { getRelativeTimeFromNow } from 'services/Utils';
import { useTranslation } from 'react-i18next';

interface StatisticWidgetStatusProps {
  qoeValue?: number;
  lastUpdated?: number;
  lastBootupTime?: number;
}

export const StatisticWidgetStatus = ({ qoeValue, lastBootupTime, lastUpdated }: StatisticWidgetStatusProps) => {
  const { t } = useTranslation();

  if (typeof qoeValue === 'number' || typeof lastUpdated === 'number' || typeof lastBootupTime === 'number') {
    const qoeStatus = convertQOEStatus(qoeValue);

    return (
      <div className='flex w-full items-center justify-start gap-1'>
        <CpeStatusIcon
          className={cn(`text-content-disabled`, {
            'text-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
            'text-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
            'text-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
          })}
        />
        <span className='flex items-center gap-1 text-xs'>
          <span
            className={cn(`text-content-disabled font-medium`, {
              'text-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
              'text-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
              'text-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
            })}>
            {t(qoeStatus)}
          </span>
          <span className='font-book text-content-primary opacity-60'>
            {t('since')} {lastUpdated ? getRelativeTimeFromNow(lastUpdated) : t('unknown')}
          </span>
          {lastBootupTime && (
            <span className='font-book text-content-primary opacity-60'>
              ({t('activeSince')} {getRelativeTimeFromNow(lastBootupTime)})
            </span>
          )}
        </span>
      </div>
    );
  }

  return (
    <div className='flex w-full items-center justify-start gap-1'>
      <CpeStatusIcon className='text-content-disabled' />
      <p className='font-book text-content-tertiary'>{t('noStatusDataAvailable')}</p>
    </div>
  );
};
