import { IInsight, trendingBarClassMap } from '@/components/Insights/insight.type';
import { cn } from '@/utils';
import { useInsightAction } from '@/components/Insights/insight.action';
import { AxonTooltipWrapper } from 'ui/UIComponents';
import { formatDate, getUnixTime } from 'services/Utils';
import { useTheme } from 'ui/UIProviders';

interface IProps {
  card: IInsight;
}

export const InsightsTrend = ({ card }: IProps) => {
  const { calculateWeeklyTrendBars } = useInsightAction();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  const { type, weeklyTrend } = card;
  const dateFormatted = (date: Date) => formatDate(getUnixTime(date) ?? 0, 'D MMM');

  const bars = calculateWeeklyTrendBars(weeklyTrend);
  return (
    <div className='flex flex-1 items-center justify-end gap-1'>
      {bars.map((bar, index) => (
        <AxonTooltipWrapper
          key={index}
          label={
            <div
              key={index}
              className={cn(
                trendingBarClassMap[type],
                'rounded-2xs h-[12px]',
                index === bars.length - 1 ? 'w-[28px]' : 'w-[6px]',
                !bar.hasData && `${isDarkMode ? 'opacity-20' : 'opacity-30'}`,
              )}
            />
          }
          content={<p>{dateFormatted(bar.date)}</p>}
        />
      ))}
    </div>
  );
};
