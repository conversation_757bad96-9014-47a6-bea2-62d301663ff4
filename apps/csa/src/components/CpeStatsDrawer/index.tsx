import useTabStore, {
  EUnit,
  useConfigWidgetCpeStatistic,
  useTabDeviceId,
  useTabLineId,
  useTimeRangeSelected,
} from '@/stores/tab.store';
import { cn } from '@/utils';
import { convertQOEStatus } from '@/utils/QOE.util';
import { ColumnDef, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useMemo } from 'react';
import { useQueryGetCpeStatsDrawer } from 'services/CPEService';
import { useGetSidebarTopology } from 'services/Topology';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import { BoldDangerCircle } from 'ui/UIAssets';
import {
  AxonDateRangePicker,
  AxonInput,
  AxonSelectWrapper,
  AxonSheet,
  AxonSheetContent,
  AxonTableData,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';

const disabledDate = {
  after: new Date(),
  before: new Date(new Date().setMonth(new Date().getMonth() - 1)),
};

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

type CpeStatsDrawerProps = {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

const CpeStatsDrawer = ({ open, onOpenChange }: CpeStatsDrawerProps) => {
  const { t } = useTranslation();
  const setTimeRange = useTabStore((state) => state.setTimeRange);

  const lineId = useTabLineId() || '';
  const deviceId = useTabDeviceId() || '';
  const startDate = useTimeRangeSelected()?.startDate;
  const endDate = useTimeRangeSelected()?.endDate;
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const searchedDeviceType = useConfigWidgetCpeStatistic()!.searchedDeviceType;
  const selectedDeviceId = useConfigWidgetCpeStatistic()!.selectedDeviceId || deviceId;

  const { data } = useGetSidebarTopology(lineId, {
    enabled: !!lineId,
  });

  const devices = useMemo(() => {
    if (data && data.data && Array.isArray(data.data.devices)) {
      return data.data.devices;
    }

    return [];
  }, [data]);

  const {
    data: queryData,
    isLoading,
    isError,
  } = useQueryGetCpeStatsDrawer(
    {
      customerId: lineId,
      deviceId: selectedDeviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: !!lineId && !!selectedDeviceId && !!startDate && !!endDate },
  );
  const QOE = queryData?.data?.QOEStatus;

  const parameters = useMemo(() => {
    if (queryData && queryData.data) {
      const parameters = Array.isArray(queryData?.data?.parameters) ? queryData?.data?.parameters : [];

      if (QOE) {
        return [
          {
            type: QOE.type,
            minMax: null,
            average: null,
            unit: '',
            latestResult: t(convertQOEStatus(QOE.latestResult)),
            hasWarning: null,
            noOfTest: null,
          },
          ...parameters,
        ];
      }

      return parameters;
    }

    return [];
  }, [QOE, queryData, t]);

  const handleSetCustomTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    setTimeRange({
      unit: EUnit.CUSTOM,
      startDate,
      endDate,
    });
  };

  const filteredParameters = useMemo(() => {
    if (searchedDeviceType) {
      return parameters.filter((p) => p.type.toLowerCase().includes(searchedDeviceType.toLowerCase()));
    }

    return parameters;
  }, [parameters, searchedDeviceType]);

  const columns = useMemo<ColumnDef<NonNullable<typeof filteredParameters>[number], any>[]>(
    () => [
      {
        header: t('device:deviceInfo.cpeStats.table.header.type'),
        accessorKey: 'type',
        cell: ({ row }) => {
          const { type } = row.original;
          return <span className='text-md text-content-primary font-medium'>{type}</span>;
        },
      },
      {
        header: t('device:deviceInfo.cpeStats.table.header.minMax'),
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { minMax, unit } = row.original;
          return <span className='text-md text-content-primary font-normal'>{minMax ? `${minMax} ${unit}` : '-'}</span>;
        },
      },
      {
        header: t('device:deviceInfo.cpeStats.table.header.average'),
        accessorKey: 'average',
        cell: ({ row }) => {
          const { average, unit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {typeof average !== 'undefined' && average !== null ? `${average} ${unit}` : '-'}
            </span>
          );
        },
      },
      {
        header: t('device:deviceInfo.cpeStats.table.header.latestResult'),
        accessorKey: 'latestResult',
        cell: ({ row }) => {
          const { latestResult, unit, hasWarning } = row.original;

          return (
            <div className='flex flex-col items-start gap-2'>
              <span className='text-content-primary'>
                {typeof latestResult !== 'undefined' && latestResult !== null ? `${latestResult} ${unit}` : '-'}
              </span>
              {hasWarning && (
                <div className='flex items-center gap-1'>
                  <BoldDangerCircle color='rgb(var(--content-meta-red))' />
                  <span className='text-content-meta-red text-xs font-medium'>
                    {t('device:deviceInfo.cpeStats.table.body.warning')}
                  </span>
                  <span className='font-book text-xs opacity-60'>
                    {t('device:deviceInfo.cpeStats.table.body.tooHigh')}
                  </span>
                </div>
              )}
            </div>
          );
        },
      },
      {
        header: t('device:deviceInfo.cpeStats.table.header.noOfTests'),
        accessorKey: 'noOfTest',
        cell: ({ row }) => {
          const { noOfTest } = row.original;
          return <span className='text-md text-content-primary font-normal'>{noOfTest}</span>;
        },
      },
    ],
    [t],
  );

  const table = useReactTable({
    columns,
    data: filteredParameters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <AxonSheet open={open} onOpenChange={onOpenChange}>
      <AxonSheetContent
        className={cn(
          'insight-axonsheet-content lg w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-lg 2xl:max-w-screen-xl',
        )}>
        <div className='flex h-screen flex-col'>
          <div className={cn('border-b-border-flat h-16 border-b px-4 py-5 text-lg font-medium')}>
            <AxonSelectWrapper
              classes={{
                selectTrigger: 'gap-xs w-fit border text-xl hover:bg-accent',
              }}
              value={selectedDeviceId}
              onChange={(newDeviceId) => addConfigEnhance('widgets.cpeStatistic.selectedDeviceId', newDeviceId)}
              options={devices.map((d) => {
                return {
                  label: `${t('device:deviceInfo.cpeStats.title')} ${d.deviceUniqueId}`,
                  value: d.deviceUniqueId,
                };
              })}
            />
          </div>
          <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
            <div className='relative w-96'>
              <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
              <AxonInput
                placeholder={t('device:deviceInfo.cpeStats.searchInputPlaceholder')}
                className='pl-10'
                value={searchedDeviceType}
                onChange={(e) => addConfigEnhance('widgets.cpeStatistic.searchedDeviceType', e.target.value)}
              />
            </div>
            <div className='ml-auto flex items-center gap-x-2'>
              <AxonDateRangePicker
                selected={{
                  from: startDate ?? undefined,
                  to: endDate ?? undefined,
                }}
                disabled={disabledDate}
                onApply={handleSetCustomTimeRange}
                showIcon={false}
              />
            </div>
          </div>

          <div className={cn('bg-surface-section h-full')}>
            <AxonTableData table={table} showFooter={false} isLoading={isLoading} isError={isError} />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default CpeStatsDrawer;
