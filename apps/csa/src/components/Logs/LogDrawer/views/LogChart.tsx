import { useLayoutEffect, useRef, useState } from 'react';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';
import * as vis from 'vis-timeline/standalone';
import 'vis-timeline/styles/vis-timeline-graph2d.min.css';
import { LogProps } from '..';
import './logChart.css';

const LogChart = ({
  logs,
  logCount,
}: LogProps & { logCount: Record<string, { count: number; icon: JSX.Element }> }) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const timelineRef = useRef<vis.Timeline | null>(null);
  const [selectedLog, setSelectedLog] = useState<LogProps['logs'][number]>();
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });

  const currentLang = localStorage.getItem('lang') ?? process.env.LANGUAGE_ENV ?? 'de';

  useLayoutEffect(() => {
    if (!containerRef.current) return;

    const formattedLogs = logs.map((l, index) => ({
      id: index,
      content: l.eventType,
      start: formatDate(l.timeStamp) ?? '',
    }));

    const items = new vis.DataSet(formattedLogs);

    const options: vis.TimelineOptions = {
      xss: { disabled: false, filterOptions: { whiteList: { p: ['class'] } } },
      height: '100%',
      align: 'left',
      margin: { item: 10, axis: 20 },
      zoomMin: 1000 * 60 * 60,
      zoomMax: 1000 * 60 * 60 * 24 * 7,
      locale: currentLang,

      template: function (item) {
        return `
        <p class="log-info log-text">
          ${item.content}
        </p>`;
      },
    };

    timelineRef.current = new vis.Timeline(containerRef.current, items, options);

    timelineRef.current.on('select', (props: { items: number[]; event: { center: { x: number; y: number } } }) => {
      const id = props.items[0];
      const selectedElement = containerRef.current?.querySelector('.vis-item.vis-selected');
      if (!selectedElement || !containerRef.current) return;
      const containerRect = containerRef.current.getBoundingClientRect();
      setSelectedLog(logs[id]);

      setPopupPosition({
        x: props.event.center.x - containerRect.left,
        y: props.event.center.y - containerRect.top,
      });
    });

    timelineRef.current.on('mouseDown', () => {
      setSelectedLog(undefined);
    });

    return () => {
      timelineRef.current?.destroy();
    };
  }, [logs, currentLang]);

  return (
    <div id='event-time-line-container' className='relative flex size-full flex-col'>
      <div id='event-time-line' ref={containerRef} className='flex-1' />

      <div className='scrollbar-lg grid h-[300px] auto-rows-[40px] grid-cols-4 overflow-auto p-6'>
        {Object.entries(logCount).map(([type, { count, icon }]) => (
          <div key={type} className='flex items-center gap-x-2'>
            {icon}
            <p className='w-[250px]'>{type}</p>
            <p>{count}</p>
          </div>
        ))}
      </div>
      {selectedLog && (
        <div
          className='bg-surface-body absolute z-10 flex flex-col gap-y-2 rounded border p-3 shadow-md'
          style={{
            top: popupPosition.y,
            left: popupPosition.x,
          }}>
          <p className='font-semibold'>{selectedLog.eventType}</p>
          <p className=''>{selectedLog.description}</p>
          <p className='text-content-tertiary'>{formatDate(selectedLog.timeStamp, DATETIME_FORMAT.DATE_TIME)}</p>
        </div>
      )}
    </div>
  );
};

export default LogChart;
