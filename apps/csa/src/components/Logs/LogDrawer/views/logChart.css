#event-time-line .vis-text,
#event-time-line .vis-item-content {
  padding: 0
}

#event-time-line .vis-item.vis-box {
  overflow: hidden;
  background-color: rgb(var(--surface-section));
  border: 1px solid var(--gradient-border);
  cursor: pointer;
  font-weight: 500;
}


#event-time-line .vis-item.vis-line {
  border-color: rgb(var(--content-disabled));
}

#event-time-line .vis-item.vis-dot {
  border-color: rgb(var(--content-disabled));
}


#event-time-line .vis-grid.vis-minor {
  border-color: var(--gradient-border)
}

#event-time-line .vis-grid.vis-major {
  border-color: rgb(var(--content-disabled))
}


#event-time-line .vis-timeline,
#event-time-line .vis-panel {
  border: 1px solid var(--gradient-border);
}

#event-time-line .vis-current-time {
  background-color: salmon
}

#event-time-line .log-success {
  background-color: rgb(var(--content-meta-green))
}

#event-time-line .log-info {
  background-color: rgb(var(--avatar-blue))
}

#event-time-line .log-error {
  background-color: rgb(var(--content-meta-red))
}

#event-time-line .log-text {
  padding: 8px;
  color: #eee
}