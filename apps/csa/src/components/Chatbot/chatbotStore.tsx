import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useShallow } from 'zustand/react/shallow';
import { ChatMessageEntity, ChatMessageToSend, createSelectors } from './types';
type ChatbotStore = {
  messages: Array<ChatMessageEntity>;
  messageHasStarted: boolean;
  isWaiting: boolean;
  chatbotUrl: string;
  sendMessage: (message: ChatMessageToSend) => void;
  updateMessage: (params: { id: string; content: string }) => void;
  startChatMessage: () => void;
  initChatbotUrl: () => void;
  sendLoadingMessage: () => void;
  removeLoadingMessage: () => void;
  modifyWaitingState: (isWaiting: boolean) => void;
};

export const useChatbotStoreBase = create<ChatbotStore>()(
  persist(
    immer(
      devtools(
        (set, get) => ({
          messages: [],
          messageHasStarted: false,
          isWaiting: false,
          chatbotUrl: '',
          initChatbotUrl: () => {
            set((state) => {
              // Only init once
              if (state.chatbotUrl) return;

              const clientId = uuidv4();
              const baseUrl = process.env.CHATBOT_WS_URL ?? `ws://vedge-staging.axon-networks.com/neura-agent/agent`;
              state.chatbotUrl = `${baseUrl}/${clientId}`;
            });
          },
          startChatMessage: () => {
            set((state) => {
              if (state.messageHasStarted) return;
              state.messageHasStarted = true;
            });
          },
          sendMessage: (message: ChatMessageToSend) => {
            set((state) => {
              const newMessage = {
                ...message,
                id: message.id || uuidv4(),
                createdAt: message['createdAt'] || new Date().toISOString(),
              };
              state.messages.push(newMessage);
            });
          },
          updateMessage: ({ id, content }) => {
            set((state) => {
              const message = state.messages.find((msg) => msg.id === id);
              if (message) {
                message.content = content;
              }
            });
          },
          sendLoadingMessage: () => {
            get().sendMessage({ owner: 'bot', content: '', type: 'loading' });
            set((state) => {
              state.isWaiting = true;
            });
          },
          modifyWaitingState: (isWaiting: boolean) => {
            set((state) => {
              state.isWaiting = isWaiting;
            });
          },
          removeLoadingMessage: () => {
            set((state) => {
              state.messages.pop();
            });
          },
        }),
        // Define instance and name for devtools
        { name: 'chatbotStore', store: 'chatbotStore' },
      ),
    ),
    {
      name: 'chatbotStore',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ messages: state.messages }),
    },
  ),
);

export const useChatbotStore = createSelectors(useChatbotStoreBase);
export const useGetMessageIds = () => useChatbotStoreBase(useShallow((state) => state.messages.map((msg) => msg.id)));
export const useGetMessageById = (id: string) =>
  useChatbotStoreBase(useShallow((state) => state.messages.find((msg) => msg.id === id)));
