import { Slice } from '../types';

export const TOTAL_SLICES = 5;
const VISUAL_CONSTANTS = {
  STROKE_WIDTH: 1,
  BASE_OFFSET: 15,
  SELECTED_OFFSET_INCREASE: 5,
  TEXT_OFFSET: 40,
  TOTAL_SLICES,
  SLICE_ANGLE: 360 / TOTAL_SLICES,
  INITIAL_ROTATION: -(360 / TOTAL_SLICES) / 2,
} as const;

const toRadians = (degrees: number) => degrees * (Math.PI / 180);

const calculateSliceAngles = (index: number) => {
  const { SLICE_ANGLE, INITIAL_ROTATION } = VISUAL_CONSTANTS;
  const startAngle = SLICE_ANGLE * index + INITIAL_ROTATION;
  const endAngle = SLICE_ANGLE * (index + 1) + INITIAL_ROTATION;
  const centerAngle = (startAngle + endAngle) / 2;
  return { startAngle, endAngle, centerAngle };
};

const calculateOffset = (isSelected: boolean, selectedSlice: number | null) => {
  const { BASE_OFFSET, SELECTED_OFFSET_INCREASE } = VISUAL_CONSTANTS;
  return selectedSlice === null ? 2 : isSelected ? BASE_OFFSET + SELECTED_OFFSET_INCREASE : 0;
};

const calculatePoint = (centerX: number, centerY: number, radius: number, angle: number) => {
  const angleRad = toRadians(angle - 90);
  return {
    x: centerX + radius * Math.cos(angleRad),
    y: centerY + radius * Math.sin(angleRad),
  };
};

const getSliceStyle = (textPosition: { x: number; y: number }): React.CSSProperties => ({
  left: `${textPosition.x}px`,
  top: `${textPosition.y}px`,
  transform: 'translate(-50%, -50%)',
  textAlign: 'center',
  fontSize: '16px',
  color: 'white',
  transformOrigin: '0% 0%',
  transition: 'all .7s',
  cursor: 'pointer',
});

const calculateTextOffset = (isSelected: boolean) => {
  const { BASE_OFFSET, SELECTED_OFFSET_INCREASE, TEXT_OFFSET } = VISUAL_CONSTANTS;
  return (isSelected ? BASE_OFFSET + SELECTED_OFFSET_INCREASE : BASE_OFFSET) + TEXT_OFFSET;
};

const calculateTextPosition = ({
  wheelRadius,
  centerPoint,
  centerAngle,
}: {
  wheelRadius: number;
  centerPoint: { x: number; y: number };
  centerAngle: number;
}) => {
  const { STROKE_WIDTH } = VISUAL_CONSTANTS;
  const radius = (wheelRadius - STROKE_WIDTH) / 2;
  const angleInRad = toRadians(centerAngle - 90);

  return {
    x: centerPoint.x + radius * Math.cos(angleInRad),
    y: centerPoint.y + radius * Math.sin(angleInRad),
  };
};

const calculateCenterPoint = ({
  offset,
  angle,
  centerPoint,
}: {
  offset: number;
  angle: number;
  centerPoint: { x: number; y: number };
}) => {
  const { SLICE_ANGLE } = VISUAL_CONSTANTS;
  const angleInRad = toRadians(angle + SLICE_ANGLE / 2 - 90);
  return {
    x: centerPoint.x + offset * Math.cos(angleInRad),
    y: centerPoint.y + offset * Math.sin(angleInRad),
  };
};

const calculateWheelConfig = (size: number) => {
  const WHEEL_RADIUS = size * 0.4;
  const CENTER_X = size / 2;
  const CENTER_Y = size / 2;
  const INNER_CIRCLE_RADIUS = WHEEL_RADIUS * 0.2;
  const OUTER_CIRCLE_PADDING = 30;

  return {
    WHEEL_RADIUS,
    CENTER_X,
    CENTER_Y,
    INNER_CIRCLE_RADIUS,
    OUTER_CIRCLE_PADDING,
    VISUAL_CONSTANTS,
  };
};

export {
  calculateCenterPoint,
  calculateOffset,
  calculatePoint,
  calculateSliceAngles,
  calculateTextOffset,
  calculateTextPosition,
  calculateWheelConfig,
  getSliceStyle,
};

export const getSliceText = (slice: Slice) => {
  switch (slice) {
    case Slice.WAN:
      return 'customer:healthCheck.wheel.sliceText.wan';
    case Slice.LAN_WLAN:
      return 'customer:healthCheck.wheel.sliceText.lanWlan';
    case Slice.CLIENTS:
      return 'customer:healthCheck.wheel.sliceText.clients';
    case Slice.SERVICES:
      return 'customer:healthCheck.wheel.sliceText.services';
    case Slice.CPE:
      return 'customer:healthCheck.wheel.sliceText.cpe';
    default:
      return 'customer:healthCheck.wheel.sliceText.unknown';
  }
};
