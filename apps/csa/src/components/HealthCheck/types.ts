import { EHealthCheckStatus } from './enum';

export type TPath = { rotationDeg: number; index: number; data: string };

export enum Slice {
  WAN = 0,
  LAN_WLAN = 1,
  CLIENTS = 2,
  SERVICES = 3,
  CPE = 4,
}

export interface HealthCheckStore {
  paths: TPath[];
  selectedSlice: Slice | null;
  setSelectedSlice: (selectedSlice: Slice | null) => void;
  nextSlice: () => void;
  prevSlice: () => void;
  setPaths: (paths: { data: string; rotationDeg: number; index: number }[]) => void;
}

export type WheelConfig = {
  CENTER_X: number;
  CENTER_Y: number;
  WHEEL_RADIUS: number;
  INNER_CIRCLE_RADIUS: number;
  OUTER_CIRCLE_PADDING: number;
  VISUAL_CONSTANTS: {
    STROKE_WIDTH: number;
    BASE_OFFSET: number;
    SELECTED_OFFSET_INCREASE: number;
    TEXT_OFFSET: number;
    TOTAL_SLICES: number;
    SLICE_ANGLE: number;
  };
};

// import { EDeviceTypes, ENodeType } from '../enums/healthCheck.enums';

export type ConnectionType = 'ethernet' | 'wifi';

export interface BaseOverview {
  stable: number | null;
  unstable: number | null;
  veryUnstable: number | null;
  disconnected: number | null;
}

export interface IHealthCheck {
  overview: IHealthCheckOverview;
  details: {
    clients: IClient[];
    services: IService[];
    networks: INetwork[];
    wans: IWan;
    cpes: ICpe[];
  };
}

export interface IHealthCheckOverview {
  clients: BaseOverview;
  services: BaseOverview;
  networks: BaseOverview;
  wans: {
    status: EHealthCheckStatus;
  };
  cpes: BaseOverview;
}

export interface IClient {
  deviceName: string;
  deviceType: string;
  deviceId: string;
  connectionType: string; // wifi, eth
  connectionInterface: string;
  connectionBand: string | null;
  stationMac: string;
  capabilities: string[];
  parentId: string;
  status: EHealthCheckStatus;
  isOnline: boolean;
}

export interface IConnection {
  status?: EHealthCheckStatus; // only for wifi connection
  connectionType: ConnectionType;
  band?: string | null;
  name?: string | null;
  duplexMode?: string | null;
  isActive: boolean;
  networkType?: string;
}

export interface INetwork {
  modelName: string;
  cpeType: string;
  cpeId: string;
  connections: IConnection[];
}

export interface IService {
  name: string;
  logo: string;
  status: EHealthCheckStatus | null;
  latency: number | null;
  jitter: number | null;
  downloadSpeed: number | null;
  uploadSpeed: number | null;
  speedStatus: EHealthCheckStatus;
  latencyStatus: EHealthCheckStatus;
  trafficStatus: EHealthCheckStatus;
  downloadTraffic: number | null;
  uploadTraffic: number | null;
}

export interface ICpe {
  modelName: string;
  cpeType: string;
  cpeId: string;
  cpeStatus: EHealthCheckStatus;
  installedTime: number | null;
  cpeFirmwareVersion: string | null;
  lastRebootTime: number | null;
  powerCycle: number | null;
  cpuDetection: number | null;
  freeMemoryDetection: number | null;
  cpuStatus: EHealthCheckStatus;
  freeMemoryStatus: EHealthCheckStatus;
  isGoldenFirmware: boolean;
  firmwareStatus: EHealthCheckStatus;
}

export interface ILinkQuality {
  isAlive: boolean;
  status: EHealthCheckStatus;
  lastBootTime: number | null;
  latency: number | null;
  latencyStatus: EHealthCheckStatus;
  jitter: number | null;
  packetLoss?: number | null;
  downCount: number | null;
  errorRate: number | null;
  errorRateStatus: EHealthCheckStatus;
}

export interface ISpeedTest {
  status: EHealthCheckStatus;
  downloadSpeed: number | null;
  uploadSpeed: number | null;
  latency: number | null;
}

export interface IBroadbandTest {
  status: EHealthCheckStatus;
}

export interface IInternetUsage {
  uploadUsage: number | null;
  downloadUsage: number | null;
}

export interface IWan {
  rootDeviceId: string;
  speedTest: ISpeedTest;
  broadbandTest: IBroadbandTest;
  linkQuality: ILinkQuality;
  internetUsage: IInternetUsage;
}
