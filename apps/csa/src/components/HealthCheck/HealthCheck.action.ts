import { useGetHealthCheck } from 'services/HealthCheck';
import { useGetRealtimeHealthcheck } from 'services/Realtime';
import { useMemo } from 'react';
import { Slice } from './types';
import { useConfigRealtime, useTabLineId } from '@/stores/tab.store';
import get from 'lodash/get';
import groupBy from 'lodash/groupBy';
import { getSliceText } from './utils/wheel.util';
import { useTranslation } from 'react-i18next';

export const useHealthCheckAction = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId() || '';

  const realtimeConfig = useConfigRealtime() ?? {};

  const {
    data: healthCheckHistoricalData,
    isLoading: isLoadingHealthCheckHistoricalData,
    ...rest
  } = useGetHealthCheck(
    {
      customerId: lineId,
    },
    { enabled: !!lineId, staleTime: Infinity },
  );

  const realtimeRequestId = get(realtimeConfig, 'realtimeRequestId', '');
  const dataIsReady = get(realtimeConfig, 'dataIsReady', false);
  const enableRealtimeRequesting = get(realtimeConfig, 'enableRealtimeRequesting', false);
  const activeProbing = get(realtimeConfig, 'activeProbing', false);

  const { data: realtimeOverviewData, isFetching: isFetchingHealthcheckRealtime } = useGetRealtimeHealthcheck(
    {
      customerId: lineId,
      realtimeRequestId,
      activeProbing,
      isActiveWifiSpeedtest: false,
    },
    {
      enabled: dataIsReady,
    },
  );

  const isRequestingRealtime = isFetchingHealthcheckRealtime || enableRealtimeRequesting;
  const healthCheckData = isRequestingRealtime ? undefined : realtimeOverviewData || healthCheckHistoricalData;
  const overview = get(healthCheckData, 'data.overview');

  const data = useMemo(() => {
    // client data
    const client = get(healthCheckData, 'data.details.clients') ?? [];
    const connectionTypeGroup = groupBy(client, 'connectionType');
    const ethernetConnection = connectionTypeGroup.ethernet?.length || 0;
    const wifiConnection = connectionTypeGroup.wifi?.length || 0;
    const numberUnknownConnectionType = client.length - ethernetConnection - wifiConnection;

    // cpe data
    const cpe = get(healthCheckData, 'data.details.cpes') ?? [];
    // lan/wlan data
    const lanWlan = get(healthCheckData, 'data.details.networks') ?? [];
    // wan data
    const wan = get(healthCheckData, 'data.details.wans');
    // service data
    const service = get(healthCheckData, 'data.details.services') ?? [];

    const result = {
      clients: {
        title: t(getSliceText(Slice.CLIENTS)),
        data: client,
        description: client.length,
        footerInfo: {
          stable: get(overview, 'clients.stable') ?? 0,
          unstable: get(overview, 'clients.unstable') ?? 0,
          veryUnstable: get(overview, 'clients.veryUnstable') ?? 0,
          disconnected: get(overview, 'clients.disconnected') ?? 0,
          ethernetConnection,
          wifiConnection,
          unknownConnection: numberUnknownConnectionType,
        },
      },
      services: {
        title: t(getSliceText(Slice.SERVICES)),
        data: service || [],
      },
      cpes: {
        title: t(getSliceText(Slice.CPE)),
        data: cpe || [],
      },
      networks: {
        title: t(getSliceText(Slice.LAN_WLAN)),
        data: lanWlan || [],
        footerInfo: {},
      },
      wans: {
        title: t(getSliceText(Slice.WAN)),
        data: wan || [],
      },
    };

    return result;
  }, [overview, healthCheckData, t]);

  return {
    ...rest,
    data,
    overview,
    isLoading: isRequestingRealtime || isLoadingHealthCheckHistoricalData,
  };
};
