import BgHealthCheck from '@/assets/BgHealthCheck';
import { useConfigHealthCheck, useTabLineId } from '@/stores/tab.store';
import { RefreshCcw } from 'lucide-react';
import { useEffect } from 'react';
import { AxonButton, AxonCard, AxonSeparator } from 'ui/UIComponents';
import { useElementSize } from 'ui/UIHooks';
import Detail from './Detail';
import { useHealthCheckAction } from './HealthCheck.action';
import Wheel from './Wheel';
import { MAP_SLICE_TO_DATA } from './constant';
import useHealthCheckStore from './store';
import { useTranslation } from 'react-i18next';

const HealthCheck = () => {
  const { t } = useTranslation();
  const setSelectedSlice = useHealthCheckStore((state) => state.setSelectedSlice);
  const selectedSlice = useHealthCheckStore((state) => state.selectedSlice);
  const lineId = useTabLineId();
  const { data, overview, isLoading } = useHealthCheckAction();
  const historicalSelectedTab = useConfigHealthCheck()?.selectedSlice;

  useEffect(() => {
    if (lineId) setSelectedSlice(historicalSelectedTab ?? null);
  }, [lineId, setSelectedSlice, historicalSelectedTab]);

  const { width: containerWidth } = useElementSize('#health-check-container');
  return (
    <AxonCard className='relative size-full overflow-hidden'>
      <BgHealthCheck className='fill-content-primary absolute bottom-0 left-0' />
      <div className='flex flex-row px-6 py-4'>
        <p data-testid='healthcheck-title' className='text-2xl font-semibold'>
          {t('customer:healthCheck.healthCheck')}{' '}
        </p>
        <AxonButton
          data-testid='healthcheck-refresh-button'
          aria-label={t('ariaLabel.reload')}
          variant='outline'
          size='icon'
          className='z-10 ml-auto'
          onClick={() => {
            setSelectedSlice(null);
          }}>
          <RefreshCcw size={16} />
        </AxonButton>
      </div>
      <AxonSeparator />

      <div id='health-check-container' className='relative flex flex-row items-center justify-center'>
        <div
          className='absolute left-10 top-10'
          style={{
            width: containerWidth / 2 - 50,
            opacity: selectedSlice !== null ? 1 : 0,
            transition: 'all .7s',
          }}>
          {selectedSlice !== null && (
            <Detail
              isLoading={isLoading}
              overview={overview?.[MAP_SLICE_TO_DATA[selectedSlice]]}
              detail={data[MAP_SLICE_TO_DATA[selectedSlice]]}
              itemType={selectedSlice}
            />
          )}
        </div>
        {Boolean(containerWidth) && (
          <div
            style={{
              transition: 'all .7s',
              transform: `translateX(${selectedSlice !== null ? containerWidth / 2 : 0}px)`,
            }}>
            <Wheel size={Math.min(containerWidth, 725)} data={overview} isLoading={isLoading} />
          </div>
        )}
      </div>
    </AxonCard>
  );
};

export default HealthCheck;
