export enum QOEStatusEnum {
  STABLE = 'stable',
  UNSTABLE = 'unstable',
  VERY_UNSTABLE = 'veryUnstable',
  UNKNOWN = 'unknown',
}

export const convertQOEStatus = (status: number | null | undefined): QOEStatusEnum => {
  if (typeof status === 'number') {
    if (status >= 4) {
      return QOEStatusEnum.STABLE;
    }

    if (status >= 2) {
      return QOEStatusEnum.UNSTABLE;
    }

    if (status >= 0) {
      return QOEStatusEnum.VERY_UNSTABLE;
    }
  }

  return QOEStatusEnum.UNKNOWN;
};
