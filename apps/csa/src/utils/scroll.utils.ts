const HEADER_OFFSET = 72; // 56px header + 16px offset

export const scrollToElement = (elementId: string, headerOffset: number = HEADER_OFFSET) => {
  const nodeToScrollTo = document.getElementById(elementId);
  if (!nodeToScrollTo) return;

  const mainContent = document.getElementById('main-content');

  if (mainContent) {
    // Calculate scroll position with header offset
    const top = nodeToScrollTo.getBoundingClientRect().top + mainContent.scrollTop - headerOffset; // 56 is the header height + 16px offset
    mainContent.scrollTo({ top, behavior: 'smooth' });
  } else {
    // Fallback to standard scrollIntoView
    nodeToScrollTo.scrollIntoView({ behavior: 'smooth' });
  }
};
