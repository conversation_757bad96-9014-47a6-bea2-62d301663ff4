import { SortingState } from '@tanstack/react-table';
import { ALL_VALUE_OPTION } from '@/components/drawers/ClientsDrawer/ClientsTable/ClientTable.action';
import { CpeBand, CpeClient } from '@/pages/Device/ClientHistory/type';

export enum ClientHistoryEMetric {
  WIFI_PHY_RATE = 'phyRate',
  WIFI_THROUGHPUT = 'throughput',
  TRAFFIC_DOWN = 'trafficDown',
  TRAFFIC_UP = 'trafficUp',
  LATENCY = 'latency',
  SNR = 'snr',
  RSSI = 'rssi',
  QOE = 'qoe',
}

export enum LogDrawerTabs {
  Logs = 'logs',
  LogList = 'logList',
  LogChart = 'logChart',
}

export enum CpeHistoryChartMetric {
  CPU_MEMORY_USAGE = 'cpuMemoryUsage',
  CHIPSET_TEMPERATURE = 'chipsetTemperature',
  POWER_CYCLE = 'powerCycle',
  QOE = 'qoe',
  RSSI = 'rssi',
  DISCONNECTION_EVENT = 'disconnEvents',
}

export enum WifiStatisticChartMetric {
  RSSI = 'rssi',
  DISCONNECTION_EVENT = 'disconnEvents',
}

export const BASE_WAN_HISTORY_METRICS = {
  STATE: 'state',
  SPEED: 'speed',
  LATENCY: 'latency',
  JITTER: 'jitter',
  TRAFFIC: 'traffic',
  PACKET_LOST: 'packetLost',
  INTERNET_USAGE: 'internetUsage',
};

export const CELLULAR_WAN_HISTORY_METRICS = {
  LTE_CELL_ID: 'lteCellId',
  LTE_BAND: 'lteBand',
  '5G_BAND': '5gBand',
  VOICE_REDUNDANCY: 'voiceRedundancy',
};

export type WidgetsConfig = {
  cpeStatistic?: {
    searchedDeviceType: string;
    selectedDeviceId: string;
  };
  cpeHistory?: {
    selectedMetric: CpeHistoryChartMetric;
  };
  wifiStatistic?: {
    selectedBandId: string;
    globalFilter: string;
  };
  lanPorts?: {
    sorting: SortingState;
  };
  wifiBands?: {
    sorting: SortingState;
  };
  lanWlanHistory?: {
    selectedBandId?: string;
    lanWlanType?: string;
  };
  wanStatistic?: {
    searchKey: string;
  };
  wanHistory?: {
    selectedMetric: string;
  };
  clientConnection?: {
    filterState: {
      search: string;
      cpeId: string;
      deviceType: string;
      connectionInterface: string;
      parentalControl: string;
      status: 'online' | 'offline' | '' | typeof ALL_VALUE_OPTION;
    };
    sorting: SortingState;
  };
  clientHistory?: {
    filterState: {
      client: CpeClient[] | null;
      metric: ClientHistoryEMetric;
      band: CpeBand | null;
    };
  };
  serviceHistory?: {
    selectedMetric: string;
  };
  insight?: {
    filterQueryParams: {
      search: string;
      deviceId: string;
      category: string;
      severity: string;
      sortTime: string;
      type: string;
    };
  };
  event?: {
    searchKeyword: string;
    sourceFilter?: string;
    eventTypeFilter?: string;
    activeDrawer: LogDrawerTabs;
  };
};

type DeepReadonly<T> = {
  readonly [P in keyof T]: DeepReadonly<T[P]>;
};

export const defaultWidgetsConfig: DeepReadonly<WidgetsConfig> = {
  cpeStatistic: {
    searchedDeviceType: '',
    selectedDeviceId: '',
  },
  cpeHistory: {
    selectedMetric: CpeHistoryChartMetric.QOE,
  },
  wifiStatistic: {
    selectedBandId: '',
    globalFilter: '',
  },
  lanPorts: {
    sorting: [],
  },
  wifiBands: {
    sorting: [],
  },
  lanWlanHistory: {
    selectedBandId: '',
    lanWlanType: 'wifiQoE',
  },
  wanStatistic: {
    searchKey: '',
  },
  wanHistory: {
    selectedMetric: BASE_WAN_HISTORY_METRICS.STATE,
  },
  clientConnection: {
    filterState: {
      search: '',
      cpeId: ALL_VALUE_OPTION,
      deviceType: ALL_VALUE_OPTION,
      connectionInterface: ALL_VALUE_OPTION,
      parentalControl: ALL_VALUE_OPTION,
      status: ALL_VALUE_OPTION,
    },
    sorting: [],
  },
  clientHistory: {
    filterState: {
      client: null,
      metric: ClientHistoryEMetric.QOE,
      band: null,
    },
  },
  serviceHistory: {
    selectedMetric: BASE_WAN_HISTORY_METRICS.SPEED,
  },
  insight: {
    filterQueryParams: {
      search: '',
      deviceId: '',
      category: '',
      severity: '',
      sortTime: 'newest',
      type: '',
    },
  },
  event: {
    searchKeyword: '',
    sourceFilter: 'all',
    eventTypeFilter: 'all',
    activeDrawer: LogDrawerTabs.Logs,
  },
};
